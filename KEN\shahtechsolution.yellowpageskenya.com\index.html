<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Shah Computers: Your One-Stop Shop for Laptops, Desktops & Accessories</title>
  <meta name="description"
    content="Shah Computers brings you top-quality new & refurbished laptops, desktops & accessories in Nairobi, affordable, reliable, and backed by warranty.">


  <meta name="keywords" content="Shah Computers, Laptops in Nairobi, Desktops in Nairobi, Computer Accessories, Refurbished Laptops, Affordable Computers, Reliable Computer Store, Computer Warranty, Nairobi Tech Store">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-09-25">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://shahtechsolution.yellowpageskenya.com">
  <meta property="og:title" content="Shah Computers: Your One-Stop Shop for Laptops, Desktops & Accessories">
  <meta property="og:description"
    content="Shah Computers brings you top-quality new & refurbished laptops, desktops & accessories in Nairobi, affordable, reliable, and backed by warranty.">
  <meta property="og:image" content="https://shahtechsolution.yellowpageskenya.com/img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://shahtechsolution.yellowpageskenya.com ">
  <meta property="twitter:title" content="Shah Computers: Your One-Stop Shop for Laptops, Desktops & Accessories ">
  <meta property="twitter:description"
    content="Shah Computers brings you top-quality new & refurbished laptops, desktops & accessories in Nairobi, affordable, reliable, and backed by warranty.">
  <meta property="twitter:image" content="https://shahtechsolution.yellowpageskenya.com/img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://shahtechsolution.yellowpageskenya.com">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://shahtechsolution.yellowpageskenya.com">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://shahtechsolution.yellowpageskenya.com/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>


  <link rel="preload" as="image" href="./img/slider_1a_scale,w_1351.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_1250.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_827.webp" fetchpriority="high">


  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <link rel="stylesheet" href="css/chooseus.css">
   <link rel="stylesheet" href="css/cta.css">
    <link rel="stylesheet" href="css/store-products.css">

  <!-- <link rel="stylesheet" href="css/categories.css"> -->
   

<!-- 
  <link rel="alternate" hreflang="en" href="https://shahtechsolution.yellowpageskenya.com/"> -->
  <link rel="alternate" hreflang="x-default" href="https://shahtechsolution.yellowpageskenya.com/">


  <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-V6PNHVJQ98"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-V6PNHVJQ98');
</script>



  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#organization",
      "name": "Shah Computers",
      "url": "https://shahtechsolution.yellowpageskenya.com",
      "logo": "https://shahtechsolution.yellowpageskenya.com/img/logo.webp",
      "description": "Shah Computers brings you top-quality new & refurbished laptops, desktops & accessories in Nairobi, affordable, reliable, and backed by warranty.",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+254113219859",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "1st Floor, Maharaja House, 01 Shivachi Road, Opp Mpshah Hospital",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "sameAs": [
        "https://www.facebook.com/profile.php?id=100087224045851&mibextid=LQQJ4d",
        "https://api.whatsapp.com/send/?phone=0113219859",
        "https://x.com/shahcomputers",
        "https://www.instagram.com/shahcomputers01",
        "https://www.youtube.com/channel/UCpnN5fUwtkfJ3CfhG0Xx-fA"
      ]
    },
    {
      "@type": "LocalBusiness",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#localbusiness",
      "name": "Shah Computers",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/logo.webp",
      "url": "https://shahtechsolution.yellowpageskenya.com",
      "telephone": "+254113219859",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "1st Floor, Maharaja House, 01 Shivachi Road, Opp Mpshah Hospital",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "openingHours": "Mo-Sa 09:00-18:00",
      "priceRange": "$$",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-1.2644254356005264",
        "longitude": "36.80834677397394"
      },
      "paymentAccepted": "Cash, Credit Card, Installment Plan"
    },
    {
      "@type": "WebSite",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#website",
      "url": "https://shahtechsolution.yellowpageskenya.com",
      "name": "Shah Computers",
      "description": "Your One-Stop Shop for Laptops, Desktops & Accessories",
      "publisher": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#organization"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://shahtechsolution.yellowpageskenya.com/?s={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@type": "WebPage",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#webpage",
      "url": "https://shahtechsolution.yellowpageskenya.com",
      "name": "Shah Computers: Your One-Stop Shop for Laptops, Desktops & Accessories",
      "isPartOf": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#website"
      },
      "about": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#organization"
      },
      "datePublished": "2025-09-25",
      "description": "Shah Computers brings you top-quality new & refurbished laptops, desktops & accessories in Nairobi, affordable, reliable, and backed by warranty.",
      "inLanguage": "en",
      "potentialAction": {
        "@type": "ReadAction",
        "target": [
          "https://shahtechsolution.yellowpageskenya.com"
        ]
      }
    },
    {
      "@type": "AboutPage",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#about",
      "url": "https://shahtechsolution.yellowpageskenya.com/#about",
      "name": "About Us",
      "description": "Learn about Shah Computers - your trusted source for new and refurbished laptops, desktops, and IT accessories in Nairobi.",
      "isPartOf": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "Organization",
        "name": "Shah Computers",
        "description": "A computer store in Nairobi specializing in wholesale and retail of the latest laptops, reliable desktops, and a wide selection of IT accessories.",
        "foundingDate": "2025-09-25",
        "serviceArea": {
          "@type": "Country",
          "name": "Kenya"
        },
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Computer Products and Services",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "New Laptops & Desktops"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Refurbished Ex-UK Devices"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Factory-Renewed Computers"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "Open-Box Units"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Product",
                "name": "IT Accessories"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Repairs & Upgrades"
              }
            }
          ]
        },
        "knowsAbout": [
          "Computer Sales",
          "Laptops",
          "Desktops",
          "Computer Accessories",
          "Refurbished Computers",
          "IT Equipment",
          "Computer Repairs"
        ],
        "values": [
          {
            "@type": "DefinedTerm",
            "name": "Quality",
            "description": "We provide top-quality new and refurbished computers with warranty."
          },
          {
            "@type": "DefinedTerm",
            "name": "Affordability",
            "description": "We offer competitive prices and flexible payment options including installment plans."
          },
          {
            "@type": "DefinedTerm",
            "name": "Reliability",
            "description": "All our devices are professionally tested and come with warranty for peace of mind."
          }
        ]
      }
    },
    {
      "@type": "Product",
      "name": "New Laptops & Desktops",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/New_Laptops_and_Desktops.webp",
      "description": "Get the latest models from trusted global brands – perfect for students, professionals, and businesses looking for performance and reliability.",
      "brand": {
        "@type": "Brand",
        "name": "Shah Computers"
      },
      "category": "Computers",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Refurbished Ex-UK Devices",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/Refurbished_Ex_UK_Devices.webp",
      "description": "High-quality, pre-owned laptops and desktops, carefully restored and rigorously tested to deliver top performance at a fraction of the price.",
      "brand": {
        "@type": "Brand",
        "name": "Shah Computers"
      },
      "category": "Computers",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Factory-Renewed Computers",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/Factory_Renewed_Computers.webp",
      "description": "Like-new machines straight from the factory, offering excellent quality with unbeatable savings.",
      "brand": {
        "@type": "Brand",
        "name": "Shah Computers"
      },
      "category": "Computers",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Open-Box Units",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/Open_Box_Units.webp",
      "description": "Unused laptops and desktops that may have minor packaging imperfections but deliver brand-new performance.",
      "brand": {
        "@type": "Brand",
        "name": "Shah Computers"
      },
      "category": "Computers",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "IT Accessories",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/IT_Accessories.webp",
      "description": "A wide selection of essentials including keyboards, mice, headphones, external drives, cables, and more – everything you need to stay connected and productive.",
      "brand": {
        "@type": "Brand",
        "name": "Shah Computers"
      },
      "category": "Computer Accessories",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Service",
      "name": "Repairs & Upgrades",
      "description": "Expert repairs and upgrades to keep your devices running smoothly and efficiently.",
      "provider": {
        "@type": "Organization",
        "name": "Shah Computers"
      },
      "serviceType": "Computer Repair",
      "category": "Technical Services"
    },
    {
      "@type": "Product",
      "name": "HP Omen 34C 34″ Curved Gaming Monitor",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/HP_Omen.webp",
      "description": "HP Omen 34C 34″ Curved Gaming Monitor",
      "brand": {
        "@type": "Brand",
        "name": "HP"
      },
      "category": "Monitors",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "Dell OptiPlex 3080 MT (Refurbished)",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/Dell_OptiPlex.webp",
      "description": "Dell OptiPlex 3080 MT (Refurbished)",
      "brand": {
        "@type": "Brand",
        "name": "Dell"
      },
      "category": "Desktops",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "Product",
      "name": "NEC VersaPro D64G9 X360 Convertible",
      "image": "https://shahtechsolution.yellowpageskenya.com/img/NEC_VersaPro.webp",
      "description": "NEC VersaPro D64G9 X360 Convertible",
      "brand": {
        "@type": "Brand",
        "name": "NEC"
      },
      "category": "Laptops",
      "offers": {
        "@type": "Offer",
        "priceCurrency": "KES",
        "availability": "https://schema.org/InStock"
      }
    },
    {
      "@type": "CollectionPage",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#products",
      "url": "https://shahtechsolution.yellowpageskenya.com/#products",
      "name": "Our Products",
      "description": "Explore our range of computer products including new laptops & desktops, refurbished Ex-UK devices, factory-renewed computers, open-box units, and IT accessories.",
      "isPartOf": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "New Laptops & Desktops",
            "url": "https://shahtechsolution.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Refurbished Ex-UK Devices",
            "url": "https://shahtechsolution.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Factory-Renewed Computers",
            "url": "https://shahtechsolution.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Open-Box Units",
            "url": "https://shahtechsolution.yellowpageskenya.com/#products"
          },
          {
            "@type": "ListItem",
            "position": 5,
            "name": "IT Accessories",
            "url": "https://shahtechsolution.yellowpageskenya.com/#products"
          }
        ]
      }
    },
    {
      "@type": "Store",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#store",
      "url": "https://shahtechsolution.yellowpageskenya.com/#store",
      "name": "Explore What's In Store",
      "description": "Visit our store to see our range of computers and accessories including HP Omen monitors, Dell OptiPlex desktops, and NEC VersaPro laptops.",
      "isPartOf": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "HP Omen 34C 34″ Curved Gaming Monitor",
            "url": "https://shahtechsolution.yellowpageskenya.com/#store"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Dell OptiPlex 3080 MT (Refurbished)",
            "url": "https://shahtechsolution.yellowpageskenya.com/#store"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "NEC VersaPro D64G9 X360 Convertible",
            "url": "https://shahtechsolution.yellowpageskenya.com/#store"
          }
        ]
      }
    },
    {
      "@type": "ContactPage",
      "@id": "https://shahtechsolution.yellowpageskenya.com/#contact",
      "url": "https://shahtechsolution.yellowpageskenya.com/#contact",
      "name": "Contact Us",
      "description": "Contact information for Shah Computers including physical address, phone number, email, and working hours.",
      "isPartOf": {
        "@id": "https://shahtechsolution.yellowpageskenya.com/#website"
      },
      "mainEntity": {
        "@type": "ContactPoint",
        "telephone": "+254113219859",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English",
        "areaServed": "KE",
        "hoursAvailable": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"
          ],
          "opens": "09:00",
          "closes": "18:00"
        }
      }
    },
    {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://shahtechsolution.yellowpageskenya.com/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "About Us",
          "item": "https://shahtechsolution.yellowpageskenya.com/#about"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Products",
          "item": "https://shahtechsolution.yellowpageskenya.com/#products"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "Contact",
          "item": "https://shahtechsolution.yellowpageskenya.com/#contact"
        }
      ]
    }
  ]
}
</script>



</head>

<body>
 
     <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="339" height="70">
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">About Us</a>
                <a href="#products">Products</a>
                <a href="#contact">Contact</a>

            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Contact Us</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="339" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                 <a href="#about">About Us</a>
                <a href="#products">Products</a>
                <a href="#contact">Contact</a>
                <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Contact Us</a>

              
            </div>
        </div>
    </div>



<section class="hero-slider">

  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_827.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_1250.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slider_1a_scale,w_1351.webp">
        <img
          src="./img/slider_1a_scale,w_1351.webp"
          alt="slider 8"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          >
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Shah Computers</span>
        <h1>Your One-Stop Shop for Laptops, Desktops & Accessories</h1>
        <p> Shah Computers offers new and refurbished laptops, desktops, and IT accessories for every need and budget.</p>
      <a  href="#products" class="explore-btn">View Products</a>
      </div>
    </div>
  </div>

 
</section>


    <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/abt_image_scale,w_200.webp"
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp" 
    srcset="./img/abt_image_scale,w_635.webp"
  >
  <img
    src="./img/abt_image_scale,w_635.webp"
    alt="Shah Computers about image"
    loading="lazy"
    title="About Shah Computers"

  >
</picture>




  </div>


</div>

                <div class="about-text">
                    <span class="abt">About Us
</span>
                    
                  <p>At Shah Computers, we make technology accessible and affordable for everyone.
</p>
                  <p>Based in Nairobi, we specialize in the wholesale and retail of the latest laptops, reliable desktops, and a wide selection of IT accessories. Whether you’re seeking new devices, professionally refurbished Ex-UK computers, certified factory-renewed models, or discounted open-box offers, we have options to suit every need.
</p>
                  <p>Choose from our selection of brand-new high-performance laptops, durable desktops, premium refurbished Ex-UK devices, certified factory-renewed computers, discounted open-box units, and essential IT accessories, among others. 
</p>
<p>Contact us today for the best value computers and laptops in Nairobi.</p>

<div class="about-services-container">
                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
             New Laptops & Desktops

                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                 Refurbished Ex-UK Devices

                            </li>

                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                     Factory-Renewed Computers
                            </li>

                          
                            

                        </ul>

                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                   Open-Box Units
                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
     IT Accessories
                            </li>



                          
                            
                        </ul>
                    </div>
          
               
                </div>
            </div>
        </div>
    </div>

    <div class="content-grid">
        <section class="why-choose-us">
            <div class="why-left-column">
                <h2 class="why-heading">Why Choose Shah Computers?
</h2>
             
                
                <div class="category active">
                    <a href="#" class="category-link">
                       Top-tier Performance, Smart Prices
                        <span class="btn-arrow">→</span>
                    </a>
                    <p class="category-description">upgraded and reliable, without the premium cost
</p>
                </div>
                
                <div class="category">
                    <h3 class="category-title">Built for Peace of Mind</h3>
                    <p class="category-description">Every device comes with a warranty and is professionally tested 
</p>
                </div>
                
                <div class="category">
                    <h3 class="category-title">Bulk Orders, Big Savings</h3>
                    <p class="category-description">Unlock exclusive discounts tailored to your needs 
</p>
                </div>
                
                <div class="category">
                    <h3 class="category-title">Kenya-Friendly Deals, Local Touch</h3>
                    <p class="category-description">Serving Nairobi and beyond with fast service and local support
</p>
                </div>
                
                <div class="category">
                    <h3 class="category-title">Flexible Payment Options</h3>
                    <p class="category-description">Enjoy convenient installment plans to make your purchase easier</p>
                </div>
            </div>
            
            <div class="why-right-column">
                <div class="main-image-choose-us">
                    <img src="./img/why_us2.webp" alt="why-choose-us image" title="why-choose-us image"  loading="lazy">
                </div>
                
                <div class="side-content">
                    <div class="feature-box">
                        <div class="feature-image">
                            <img src="./img/why_us_sm.webp" alt="why-choose-us image" title="why-choose-us image" loading="lazy">
                        </div>
                        <div class="feature-content">
                            <!-- <h3 class="feature-heading">Shop with Shah Computers</h3> -->
                            <h4 class="feature-subheading">Shop with Shah Computers</h4>
                            <p class="feature-location">Quality laptops, desktops & accessories available</p>
                            <div class="feature-contact">
                                <div class="contact-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="contact-icon">
                                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                                        <polyline points="22,6 12,13 2,6"></polyline>
                                    </svg>
                                    <a href="mailto:<EMAIL>" class="contact-link"><EMAIL></a>
                                </div>
                                <div class="contact-item">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="contact-icon">
                                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                                    </svg>
                                    <a href="tel:0113219859" class="contact-link">0113219859</a>
                                </div>
                            </div>
                            <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="btn">Shop Now <span class="btn-arrow">→</span></a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>

    
 <div class="fd-cta-container">
  <div class="fd-image-section">
    <div class="fd-image-bg lazy-background" data-bg="./img/cta_image.webp"></div>
    <div class="fd-image-overlay"></div>
  </div>

  <div class="fd-content-section">
    <div class="fd-patterny-overlay"></div>

    <div>
      <h2>Trust & Support You Can Count On </h2>
      <p>Repairs & Upgrades - Expert repairs and upgrades to keep your devices running smoothly and efficiently. </p>
      <p>Pay in Installments - Get your device now and pay in easy, flexible installments.</p>
      <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="fd-cta-button">START NOW <span class="fd-arrow">→</span></a>
    </div>
  </div>
</div>




 <div class="storeprod-header" id="store">
  <div class="storeprod-container">
    <h2 class="storeprod-section-title">Explore What’s In Store
</h2>

    <div class="content-grid">
      <div class="storeprod-grid">
        <div class="storeprod-card">
          <img src="./img/HP_Omen.webp" alt="HP Omen 34C 34″ Curved Gaming Monitor" title="HP Omen 34C 34″ Curved Gaming Monitor" loading="lazy">
          <div class="storeprod-content">
            <p class="storeprod-text">HP Omen 34C 34″ Curved Gaming Monitor</p>
          
          </div>
        </div>

        <div class="storeprod-card">
          <img src="./img/Dell_OptiPlex.webp" alt="Dell OptiPlex 3080 MT (Refurbished)" title="Dell OptiPlex 3080 MT (Refurbished)" loading="lazy">
          <div class="storeprod-content">
            <p class="storeprod-text">Dell OptiPlex 3080 MT (Refurbished) 
</p>
           
          </div>
        </div>

        <div class="storeprod-card">
          <img src="./img/NEC_VersaPro.webp" alt="NEC VersaPro D64G9 X360 Convertible" title="NEC VersaPro D64G9 X360 Convertible" loading="lazy">
          <div class="storeprod-content">
            <p class="storeprod-text">NEC VersaPro D64G9 X360 Convertible 
</p>
           
          </div>
        </div>

      
      </div>
    </div>
  </div>
</div>



   <div class="fireprod-header" id="products">
        <div class="fireprod-container">
            <h2 class="fireprod-section-title">Our Products
</h2>

            <div class="content-grid">
                <div class="fireprod-grid">
                    


                    <div class="fireprod-card">
                        <img src="./img/New_Laptops_and_Desktops.webp" alt="New Laptops & Desktops" title="New Laptops & Desktops" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">New Laptops & Desktops</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Get the latest models from trusted global brands – perfect for students, professionals, and businesses looking for performance and reliability.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/Refurbished_Ex_UK_Devices.webp" alt="Refurbished Ex-UK Devices" title="Refurbished Ex-UK Devices"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Refurbished Ex-UK Devices</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>High-quality, pre-owned laptops and desktops, carefully restored and rigorously tested to deliver top performance at a fraction of the price.


                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/Factory_Renewed_Computers.webp" alt="Factory-Renewed Computers" title="Factory-Renewed Computers"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Factory-Renewed Computers</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>Like-new machines straight from the factory, offering excellent quality with unbeatable savings.



                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    
                    <div class="fireprod-card">
                        <img src="./img/Open_Box_Units.webp" alt="Open-Box Units" title="Open-Box Units" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Open-Box Units</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Unused laptops and desktops that may have minor packaging imperfections but deliver brand-new performance.
                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                      <div class="fireprod-card">
                        <img src="./img/IT_Accessories.webp" alt="IT Accessories" title="IT Accessories" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">IT Accessories</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>A wide selection of essentials including keyboards, mice, headphones, external drives, cables, and more – everything you need to stay connected and productive.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>
                   
                  

                </div>
            </div>
        </div>
    </div>


  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contact Us
</h2>
      <div class="contact-content">
        <div class="contact-map">


          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.846811671728!2d36.80834677397394!3d-1.2644254356005264!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f17d0be29b2f5%3A0x4e1fabace1321cdb!2sShah%20Computers!5e0!3m2!1sen!2ske!4v1759230707577!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="Shah Computers  Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Physical Address</h3>
              <p>1st Floor, Maharaja House, 01 Shivachi Road, Opp Mpshah Hospital, Nairobi</p>


            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Phone number</h3>
              <p><a href="tel:0113219859">0113219859
</a></p>



            
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Office Hours</h3>
              <p>Monday to Saturday: 9:00am – 6:00pm </p>

<p>Sundays: Closed
</p>


            </div>
          </div>
          <!-- Social Media Icons -->

          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Connect With Us</h3>


              <div class="social-links">
              
             
                  <!-- Whatsapp -->
               <a href="https://api.whatsapp.com/send/?phone=0113219859&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.25.018.58.157 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>
              
             
                <a href="https://www.facebook.com/profile.php?id=100087224045851&mibextid=LQQJ4d" target="_blank" class="social-link" aria-label="Facebook">
                
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.5 13.478a5.5 5.5 0 1 0-1.5-.069V9.75H5.75a.75.75 0 0 1 0-1.5H7V7.24c0-.884.262-1.568.722-2.032S8.843 4.5 9.644 4.5c.273 0 .612.04.948.213a.75.75 0 0 1-.685 1.334A.6.6 0 0 0 9.644 6c-.493 0-.737.144-.857.265c-.12.12-.287.39-.287.975v1.01h1.25a.75.75 0 0 1 0 1.5H8.5zM8 15A7 7 0 1 0 8 1a7 7 0 0 0 0 14" clip-rule="evenodd"></path></svg>
                
                </a>

             

                <!-- Twitter -->
                <a href="https://x.com/shahcomputers" target="_blank" class="social-link" aria-label="Twitter">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M2.5 12c0-4.478 0-6.718 1.391-8.109S7.521 2.5 12.001 2.5c4.478 0 6.717 0 8.108 1.391S21.5 7.521 21.5 12c0 4.478 0 6.718-1.391 8.109S16.479 21.5 12 21.5c-4.478 0-6.717 0-8.109-1.391c-1.39-1.392-1.39-3.63-1.39-8.109"></path>
                      <path d="m7 17l4.194-4.193M17 7l-4.193 4.194m0 0L9.777 7H7l4.194 5.807m1.613-1.614L17 17h-2.778l-3.028-4.193"></path>
                    </g>
                  </svg>
                </a>
                <!-- Instagram -->
                <a href="https://www.instagram.com/shahcomputers01/#
" target="_blank" class="social-link" aria-label="Instagram">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M16 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M12 7.25a4.75 4.75 0 1 0 0 9.5a4.75 4.75 0 0 0 0-9.5M8.75 12a3.25 3.25 0 1 1 6.5 0a3.25 3.25 0 0 1-6.5 0" clip-rule="evenodd"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M17.258 2.833a47.7 47.7 0 0 0-10.516 0c-2.012.225-3.637 1.81-3.873 3.832a46 46 0 0 0 0 10.67c.236 2.022 1.86 3.607 3.873 3.832a47.8 47.8 0 0 0 10.516 0c2.012-.225 3.637-1.81 3.873-3.832a46 46 0 0 0 0-10.67c-.236-2.022-1.86-3.607-3.873-3.832m-10.35 1.49a46.2 46.2 0 0 1 10.184 0c1.33.15 2.395 1.199 2.55 2.517a44.4 44.4 0 0 1 0 10.32a2.89 2.89 0 0 1-2.55 2.516a46.2 46.2 0 0 1-10.184 0a2.89 2.89 0 0 1-2.55-2.516a44.4 44.4 0 0 1 0-10.32a2.89 2.89 0 0 1 2.55-2.516" clip-rule="evenodd"></path>
                  </svg>
                </a>


                 <!-- YouTube -->
                <a href="https://www.youtube.com/channel/UCpnN5fUwtkfJ3CfhG0Xx-fA" target="_blank" class="social-link" aria-label="YouTube">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="none" stroke="currentColor" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="1.5" d="M22.54 6.42a2.77 2.77 0 0 0-1.945-1.957C18.88 4 12 4 12 4s-6.88 0-8.595.463A2.77 2.77 0 0 0 1.46 6.42C1 8.148 1 11.75 1 11.75s0 3.602.46 5.33a2.77 2.77 0 0 0 1.945 1.958C5.121 19.5 12 19.5 12 19.5s6.88 0 8.595-.462a2.77 2.77 0 0 0 1.945-1.958c.46-1.726.46-5.33.46-5.33s0-3.602-.46-5.33ZM9.75 15.021V8.48l5.75 3.271z"></path>
                  </svg>
                </a>
              
               
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy;<span id="current-year"></span> Shah Computers. All Rights Reserved.</p>

        </div>
        <div class="designer">
          <a href="https://www.yellowpageskenya.com/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50"
              title="Yellow Pages Kenya">
            <p>Powered by Yellow Pages Kenya</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script src="./js/testimonial.js"></script>
  <!-- <script src="./js/main.js"></script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
<script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

 


</body>

</html>