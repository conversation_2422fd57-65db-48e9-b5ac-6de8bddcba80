document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    const indicators = document.querySelectorAll('.indicator');
    const prevBtn = document.querySelector('.prev-slide');
    const nextBtn = document.querySelector('.next-slide');
    let currentSlide = 0;
    let slideInterval;

    // Function to show a specific slide with improved transition
    function showSlide(index) {
        // Fade out current slide smoothly
        slides[currentSlide].style.opacity = '0';
        
        // Small delay before showing new slide to prevent jarring transition
        setTimeout(() => {
            // Remove active class from all slides and indicators
            slides.forEach(slide => slide.classList.remove('active'));
            indicators.forEach(indicator => indicator.classList.remove('active'));
            
            // Add active class to current slide and indicator
            slides[index].classList.add('active');
            indicators[index].classList.add('active');
            
            // Force repaint before adding opacity for smoother transition
            void slides[index].offsetWidth;
            
            // Fade in the new slide
            slides[index].style.opacity = '1';
            
            // Update current slide index
            currentSlide = index;
        }, 50); // Very short delay, just enough to separate the transitions
    }

    // Function to show next slide
    function nextSlide() {
        let next = currentSlide + 1;
        if (next >= slides.length) {
            next = 0;
        }
        showSlide(next);
    }

    // Function to show previous slide
    function prevSlide() {
        let prev = currentSlide - 1;
        if (prev < 0) {
            prev = slides.length - 1;
        }
        showSlide(prev);
    }

    // Start automatic slide transition
    function startSlideShow() {
        slideInterval = setInterval(nextSlide, 5000); // Change slide every 5 seconds
    }

    // Stop automatic slide transition
    function stopSlideShow() {
        clearInterval(slideInterval);
    }

    // Event listeners for indicators
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            stopSlideShow();
            showSlide(parseInt(this.dataset.index));
            startSlideShow();
        });
    });

    // Event listener for previous button
    prevBtn.addEventListener('click', function() {
        stopSlideShow();
        prevSlide();
        startSlideShow();
    });

    // Event listener for next button
    nextBtn.addEventListener('click', function() {
        stopSlideShow();
        nextSlide();
        startSlideShow();
    });

    // Set initial state - ensure first slide is visible
    slides[0].style.opacity = '1';

    // Start the slideshow
    startSlideShow();
});