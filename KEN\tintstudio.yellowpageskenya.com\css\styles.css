/*!
 * Bootstrap v4.5.3 (https://getbootstrap.com/)
 * Copyright 2011-2020 The Bootstrap Authors
 * Copyright 2011-2020 Twitter, Inc.
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */

 :root {
    --blue: #007bff;
    --indigo: #6610f2;
    --purple: #6f42c1;
    --pink: #e83e8c;
    --red: #fede4d;
    --orange: #fd7e14;
    --yellow: #fede4d;
    --green: #28a745;
    --teal: #20c997;
    --cyan: #17a2b8;
    --white: #fff;
    --gray: #6c757d;
    --gray-dark: #343a40;
    --primary: #505879;
    --secondary: #f2f2f4;
    --success: #28a745;
    --info: #17a2b8;
    --warning: #fede4d;
    --danger: #fede4d;
    --light: #ffffff;
    --dark: #1f1f2e;
    --breakpoint-xs: 0;
    --breakpoint-sm: 576px;
    --breakpoint-md: 768px;
    --breakpoint-lg: 992px;
    --breakpoint-xl: 1400px;
    --font-family-sans-serif: 'Poppins', sans-serif;
    --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace
}

*,
::after,
::before {
    box-sizing: border-box
}

html {
    font-family: sans-serif;
    line-height: 1.15;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent
}

article,
footer,
nav,
section {
    display: block
}

body {
    margin: 0;
    font-family: Poppins, sans-serif;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #757575;
    text-align: left;
    background-color: #fff
}

[tabindex='-1']:focus:not(:focus-visible) {
    outline: 0 !important
}

h1,
h2,
h3,
h4,
h5,
h6 {
    margin-top: 0;
    margin-bottom: .5rem
}

p {
    margin-top: 0;
    margin-bottom: 1rem
}

address {
    margin-bottom: 1rem;
    font-style: normal;
    line-height: inherit
}

ul {
    margin-top: 0;
    margin-bottom: 1rem
}

ul ul {
    margin-bottom: 0
}

b,
strong {
    font-weight: bolder
}

small {
    font-size: 80%
}

a {
    color: #505879;
    text-decoration: none;
    background-color: transparent
}

a:hover {
    color: #b33200;
    text-decoration: underline
}

a:not([href]):not([class]) {
    color: inherit;
    text-decoration: none
}

a:not([href]):not([class]):hover {
    color: inherit;
    text-decoration: none
}

img {
    vertical-align: middle;
    border-style: none
}

label {
    display: inline-block;
    margin-bottom: .5rem
}

button {
    border-radius: 0
}

button:focus {
    outline: 1px dotted;
    outline: 5px auto -webkit-focus-ring-color
}

button {
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    line-height: inherit
}

button {
    overflow: visible
}

button {
    text-transform: none
}

[role=button] {
    cursor: pointer
}

[type=button],
[type=reset],
[type=submit],
button {
    -webkit-appearance: button
}

[type=button]:not(:disabled),
[type=reset]:not(:disabled),
[type=submit]:not(:disabled),
button:not(:disabled) {
    cursor: pointer
}

[type=button]::-moz-focus-inner,
[type=reset]::-moz-focus-inner,
[type=submit]::-moz-focus-inner,
button::-moz-focus-inner {
    padding: 0;
    border-style: none
}

[type=number]::-webkit-inner-spin-button,
[type=number]::-webkit-outer-spin-button {
    height: auto
}

[type=search] {
    outline-offset: -2px;
    -webkit-appearance: none
}

[type=search]::-webkit-search-decoration {
    -webkit-appearance: none
}

::-webkit-file-upload-button {
    font: inherit;
    -webkit-appearance: button
}

summary {
    display: list-item;
    cursor: pointer
}

[hidden] {
    display: none !important
}

.h1,
.h2,
.h3,
.h4,
.h5,
.h6,
h1,
h2,
h3,
h4,
h5,
h6 {
    margin-bottom: .5rem;
    font-weight: 500;
    line-height: 1.2;
    color: #1f1f2e
}

.h1,
h1 {
    font-size: 2.5rem
}

@media (max-width:1200px) {
    .h1,
    h1 {
        font-size: calc(1.375rem + 1.5vw)
    }
}

.h2,
h2 {
    font-size: 2rem
}

@media (max-width:1200px) {
    .h2,
    h2 {
        font-size: calc(1.325rem + .9vw)
    }
}

.h3,
h3 {
    font-size: 1.75rem
}

@media (max-width:1200px) {
    .h3,
    h3 {
        font-size: calc(1.3rem + .6vw)
    }
}

.h4,
h4 {
    font-size: 1.5rem
}

@media (max-width:1200px) {
    .h4,
    h4 {
        font-size: calc(1.275rem + .3vw)
    }
}

.h5,
h5 {
    font-size: 1.25rem
}

.h6,
h6 {
    font-size: 1rem
}

.display-1 {
    font-size: 6rem;
    font-weight: 300;
    line-height: 1.2
}

@media (max-width:1200px) {
    .display-1 {
        font-size: calc(1.725rem + 5.7vw)
    }
}

.display-2 {
    font-size: 5.5rem;
    font-weight: 300;
    line-height: 1.2
}

@media (max-width:1200px) {
    .display-2 {
        font-size: calc(1.675rem + 5.1vw)
    }
}

.display-3 {
    font-size: 4.5rem;
    font-weight: 300;
    line-height: 1.2
}

@media (max-width:1200px) {
    .display-3 {
        font-size: calc(1.575rem + 3.9vw)
    }
}

.display-4 {
    font-size: 3.5rem;
    font-weight: 300;
    line-height: 1.2
}

@media (max-width:1200px) {
    .display-4 {
        font-size: calc(1.475rem + 2.7vw)
    }
}

.small,
small {
    font-size: 80%;
    font-weight: 400
}

.container,
.container-lg {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto
}

@media (min-width:576px) {
    .container {
        max-width: 540px
    }
}

@media (min-width:768px) {
    .container {
        max-width: 720px
    }
}

@media (min-width:992px) {
    .container,
    .container-lg {
        max-width: 960px
    }
}

@media (min-width:1200px) {
    .container,
    .container-lg {
        max-width: 1140px
    }
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px
}

.col,
.col-1,
.col-10,
.col-11,
.col-12,
.col-2,
.col-3,
.col-4,
.col-5,
.col-6,
.col-7,
.col-8,
.col-9,
.col-auto,
.col-lg,
.col-lg-1,
.col-lg-10,
.col-lg-11,
.col-lg-12,
.col-lg-2,
.col-lg-3,
.col-lg-4,
.col-lg-5,
.col-lg-6,
.col-lg-7,
.col-lg-8,
.col-lg-9,
.col-lg-auto {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px
}

.col {
    flex-basis: 0;
    flex-grow: 1;
    max-width: 100%
}

.col-auto {
    flex: 0 0 auto;
    width: auto;
    max-width: 100%
}

.col-1 {
    flex: 0 0 8.33333%;
    max-width: 8.33333%
}

.col-2 {
    flex: 0 0 16.66667%;
    max-width: 16.66667%
}

.col-3 {
    flex: 0 0 25%;
    max-width: 25%
}

.col-4 {
    flex: 0 0 33.33333%;
    max-width: 33.33333%
}

.col-5 {
    flex: 0 0 41.66667%;
    max-width: 41.66667%
}

.col-6 {
    flex: 0 0 50%;
    max-width: 50%
}

.col-7 {
    flex: 0 0 58.33333%;
    max-width: 58.33333%
}

.col-8 {
    flex: 0 0 66.66667%;
    max-width: 66.66667%
}

.col-9 {
    flex: 0 0 75%;
    max-width: 75%
}

.col-10 {
    flex: 0 0 83.33333%;
    max-width: 83.33333%
}

.col-11 {
    flex: 0 0 91.66667%;
    max-width: 91.66667%
}

.col-12 {
    flex: 0 0 100%;
    max-width: 100%
}

@media (min-width:992px) {
    .col-lg {
        flex-basis: 0;
        flex-grow: 1;
        max-width: 100%
    }
    .col-lg-auto {
        flex: 0 0 auto;
        width: auto;
        max-width: 100%
    }
    .col-lg-1 {
        flex: 0 0 8.33333%;
        max-width: 8.33333%
    }
    .col-lg-2 {
        flex: 0 0 16.66667%;
        max-width: 16.66667%
    }
    .col-lg-3 {
        flex: 0 0 25%;
        max-width: 25%
    }
    .col-lg-4 {
        flex: 0 0 33.33333%;
        max-width: 33.33333%
    }
    .col-lg-5 {
        flex: 0 0 41.66667%;
        max-width: 41.66667%
    }
    .col-lg-6 {
        flex: 0 0 50%;
        max-width: 50%
    }
    .col-lg-7 {
        flex: 0 0 58.33333%;
        max-width: 58.33333%
    }
    .col-lg-8 {
        flex: 0 0 66.66667%;
        max-width: 66.66667%
    }
    .col-lg-9 {
        flex: 0 0 75%;
        max-width: 75%
    }
    .col-lg-10 {
        flex: 0 0 83.33333%;
        max-width: 83.33333%
    }
    .col-lg-11 {
        flex: 0 0 91.66667%;
        max-width: 91.66667%
    }
    .col-lg-12 {
        flex: 0 0 100%;
        max-width: 100%
    }
}

.btn {
    display: inline-block;
    font-weight: 400;
    color: #757575;
    text-align: center;
    vertical-align: middle;
    user-select: none;
    background-color: transparent;
    border: 1px solid transparent;
    padding: .375rem .75rem;
    font-size: 1rem;
    line-height: 1.5;
    border-radius: 0;
    transition: color .15s ease-in-out, background-color .15s ease-in-out, border-color .15s ease-in-out, box-shadow .15s ease-in-out
}

@media (prefers-reduced-motion:reduce) {
    .btn {
        transition: none
    }
}

.btn:hover {
    color: #757575;
    text-decoration: none
}

.btn:focus {
    outline: 0;
    box-shadow: 0 0 0 .2rem rgba(255, 72, 0, .25)
}

.btn:disabled {
    opacity: .65
}

.btn:not(:disabled):not(.disabled) {
    cursor: pointer
}

.btn-primary {
    color: #fff;
    background-color: #505879;
    border-color: #505879
}

.btn-primary:hover {
    color: #fff;
    background-color: #d93d00;
    border-color: #cc3a00
}

.btn-primary:focus {
    color: #fff;
    background-color: #d93d00;
    border-color: #cc3a00;
    box-shadow: 0 0 0 .2rem rgba(255, 99, 38, .5)
}

.btn-primary:disabled {
    color: #fff;
    background-color: #505879;
    border-color: #505879
}

.btn-primary:not(:disabled):not(.disabled).active,
.btn-primary:not(:disabled):not(.disabled):active {
    color: #fff;
    background-color: #cc3a00;
    border-color: #bf3600
}

.btn-primary:not(:disabled):not(.disabled).active:focus,
.btn-primary:not(:disabled):not(.disabled):active:focus {
    box-shadow: 0 0 0 .2rem rgba(255, 99, 38, .5)
}

.btn-link {
    font-weight: 400;
    color: #505879;
    text-decoration: none
}

.btn-link:hover {
    color: #b33200;
    text-decoration: underline
}

.btn-link:focus {
    text-decoration: underline
}

.btn-link:disabled {
    color: #6c757d;
    pointer-events: none
}

.btn-lg {
    padding: .5rem 1rem;
    font-size: 1.25rem;
    line-height: 1.5;
    border-radius: 0
}

.nav {
    display: flex;
    flex-wrap: wrap;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.nav-link {
    display: block;
    padding: .5rem 1rem
}

.nav-link:focus,
.nav-link:hover {
    text-decoration: none
}

.navbar {
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between;
    padding: .5rem 1rem
}

.navbar .container,
.navbar .container-lg {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-between
}

.navbar-nav {
    display: flex;
    flex-direction: column;
    padding-left: 0;
    margin-bottom: 0;
    list-style: none
}

.navbar-nav .nav-link {
    padding-right: 0;
    padding-left: 0
}

.navbar-text {
    display: inline-block;
    padding-top: .5rem;
    padding-bottom: .5rem
}

.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid rgba(0, 0, 0, .125)
}

.card-body {
    flex: 1 1 auto;
    min-height: 1px;
    padding: 1.25rem
}

.card-title {
    margin-bottom: .75rem
}

.card-text:last-child {
    margin-bottom: 0
}

.card-link:hover {
    text-decoration: none
}

.card-link+.card-link {
    margin-left: 1.25rem
}

.card-footer {
    padding: .75rem 1.25rem;
    background-color: rgba(0, 0, 0, .03);
    border-top: 1px solid rgba(0, 0, 0, .125)
}

.card-img,
.card-img-top {
    flex-shrink: 0;
    width: 100%
}

@keyframes progress-bar-stripes {
    from {
        background-position: 1rem 0
    }
    to {
        background-position: 0 0
    }
}

@keyframes spinner-border {
    to {
        transform: rotate(360deg)
    }
}

@keyframes spinner-grow {
    0% {
        transform: scale(0)
    }
    50% {
        opacity: 1;
        transform: none
    }
}

.align-top {
    vertical-align: top !important
}

.align-text-top {
    vertical-align: text-top !important
}

.border {
    border: 1px solid #dee2e6 !important
}

.border-top {
    border-top: 1px solid #dee2e6 !important
}

.border-right {
    border-right: 1px solid #dee2e6 !important
}

.border-left {
    border-left: 1px solid #dee2e6 !important
}

.border-0 {
    border: 0 !important
}

.border-top-0 {
    border-top: 0 !important
}

.border-right-0 {
    border-right: 0 !important
}

.border-left-0 {
    border-left: 0 !important
}

.border-primary {
    border-color: #505879 !important
}

.border-white {
    border-color: #fff !important
}

.d-none {
    display: none !important
}

.d-flex {
    display: flex !important
}

@media (min-width:992px) {
    .d-lg-none {
        display: none !important
    }
    .d-lg-flex {
        display: flex !important
    }
}

.flex-row {
    flex-direction: row !important
}

.align-content-center {
    align-content: center !important
}

@media (min-width:992px) {
    .flex-lg-row {
        flex-direction: row !important
    }
    .align-content-lg-center {
        align-content: center !important
    }
}

.position-relative {
    position: relative !important
}

.position-sticky {
    position: sticky !important
}

@supports (position:sticky) {
    .sticky-top {
        position: sticky;
        top: 0;
        z-index: 1020
    }
}

.shadow {
    box-shadow: 0 .5rem 1rem rgba(0, 0, 0, .15) !important
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, .175) !important
}

.shadow-none {
    box-shadow: none !important
}

.h-25 {
    height: 25% !important
}

.h-50 {
    height: 50% !important
}

.h-75 {
    height: 75% !important
}

.h-100 {
    height: 100% !important
}

.h-auto {
    height: auto !important
}

.m-0 {
    margin: 0 !important
}

.mb-0 {
    margin-bottom: 0 !important
}

.m-1 {
    margin: .25rem !important
}

.mb-1 {
    margin-bottom: .25rem !important
}

.m-2 {
    margin: .5rem !important
}

.mb-2 {
    margin-bottom: .5rem !important
}

.m-3 {
    margin: 1rem !important
}

.mb-3 {
    margin-bottom: 1rem !important
}

.m-4 {
    margin: 1.5rem !important
}

.mb-4 {
    margin-bottom: 1.5rem !important
}

.m-5 {
    margin: 3rem !important
}

.mb-5 {
    margin-bottom: 3rem !important
}

.p-0 {
    padding: 0 !important
}

.pt-0 {
    padding-top: 0 !important
}

.px-0 {
    padding-right: 0 !important
}

.pb-0 {
    padding-bottom: 0 !important
}

.px-0 {
    padding-left: 0 !important
}

.p-1 {
    padding: .25rem !important
}

.pt-1 {
    padding-top: .25rem !important
}

.px-1 {
    padding-right: .25rem !important
}

.pb-1 {
    padding-bottom: .25rem !important
}

.px-1 {
    padding-left: .25rem !important
}

.p-2 {
    padding: .5rem !important
}

.pt-2 {
    padding-top: .5rem !important
}

.px-2 {
    padding-right: .5rem !important
}

.pb-2 {
    padding-bottom: .5rem !important
}

.px-2 {
    padding-left: .5rem !important
}

.p-3 {
    padding: 1rem !important
}

.pt-3 {
    padding-top: 1rem !important
}

.px-3 {
    padding-right: 1rem !important
}

.pb-3 {
    padding-bottom: 1rem !important
}

.px-3 {
    padding-left: 1rem !important
}

.p-4 {
    padding: 1.5rem !important
}

.pt-4 {
    padding-top: 1.5rem !important
}

.px-4 {
    padding-right: 1.5rem !important
}

.pb-4 {
    padding-bottom: 1.5rem !important
}

.px-4 {
    padding-left: 1.5rem !important
}

.p-5 {
    padding: 3rem !important
}

.pt-5 {
    padding-top: 3rem !important
}

.px-5 {
    padding-right: 3rem !important
}

.pb-5 {
    padding-bottom: 3rem !important
}

.px-5 {
    padding-left: 3rem !important
}

.m-n1 {
    margin: -.25rem !important
}

.mb-n1 {
    margin-bottom: -.25rem !important
}

.m-n2 {
    margin: -.5rem !important
}

.mb-n2 {
    margin-bottom: -.5rem !important
}

.m-n3 {
    margin: -1rem !important
}

.mb-n3 {
    margin-bottom: -1rem !important
}

.m-n4 {
    margin: -1.5rem !important
}

.mb-n4 {
    margin-bottom: -1.5rem !important
}

.m-n5 {
    margin: -3rem !important
}

.mb-n5 {
    margin-bottom: -3rem !important
}

.m-auto {
    margin: auto !important
}

.mb-auto {
    margin-bottom: auto !important
}

@media (min-width:992px) {
    .m-lg-0 {
        margin: 0 !important
    }
    .mb-lg-0 {
        margin-bottom: 0 !important
    }
    .m-lg-1 {
        margin: .25rem !important
    }
    .mb-lg-1 {
        margin-bottom: .25rem !important
    }
    .m-lg-2 {
        margin: .5rem !important
    }
    .mb-lg-2 {
        margin-bottom: .5rem !important
    }
    .m-lg-3 {
        margin: 1rem !important
    }
    .mb-lg-3 {
        margin-bottom: 1rem !important
    }
    .m-lg-4 {
        margin: 1.5rem !important
    }
    .mb-lg-4 {
        margin-bottom: 1.5rem !important
    }
    .m-lg-5 {
        margin: 3rem !important
    }
    .mb-lg-5 {
        margin-bottom: 3rem !important
    }
    .p-lg-0 {
        padding: 0 !important
    }
    .pt-lg-0 {
        padding-top: 0 !important
    }
    .px-lg-0 {
        padding-right: 0 !important
    }
    .pb-lg-0 {
        padding-bottom: 0 !important
    }
    .px-lg-0 {
        padding-left: 0 !important
    }
    .p-lg-1 {
        padding: .25rem !important
    }
    .pt-lg-1 {
        padding-top: .25rem !important
    }
    .px-lg-1 {
        padding-right: .25rem !important
    }
    .pb-lg-1 {
        padding-bottom: .25rem !important
    }
    .px-lg-1 {
        padding-left: .25rem !important
    }
    .p-lg-2 {
        padding: .5rem !important
    }
    .pt-lg-2 {
        padding-top: .5rem !important
    }
    .px-lg-2 {
        padding-right: .5rem !important
    }
    .pb-lg-2 {
        padding-bottom: .5rem !important
    }
    .px-lg-2 {
        padding-left: .5rem !important
    }
    .p-lg-3 {
        padding: 1rem !important
    }
    .pt-lg-3 {
        padding-top: 1rem !important
    }
    .px-lg-3 {
        padding-right: 1rem !important
    }
    .pb-lg-3 {
        padding-bottom: 1rem !important
    }
    .px-lg-3 {
        padding-left: 1rem !important
    }
    .p-lg-4 {
        padding: 1.5rem !important
    }
    .pt-lg-4 {
        padding-top: 1.5rem !important
    }
    .px-lg-4 {
        padding-right: 1.5rem !important
    }
    .pb-lg-4 {
        padding-bottom: 1.5rem !important
    }
    .px-lg-4 {
        padding-left: 1.5rem !important
    }
    .p-lg-5 {
        padding: 3rem !important
    }
    .pt-lg-5 {
        padding-top: 3rem !important
    }
    .px-lg-5 {
        padding-right: 3rem !important
    }
    .pb-lg-5 {
        padding-bottom: 3rem !important
    }
    .px-lg-5 {
        padding-left: 3rem !important
    }
    .m-lg-n1 {
        margin: -.25rem !important
    }
    .mb-lg-n1 {
        margin-bottom: -.25rem !important
    }
    .m-lg-n2 {
        margin: -.5rem !important
    }
    .mb-lg-n2 {
        margin-bottom: -.5rem !important
    }
    .m-lg-n3 {
        margin: -1rem !important
    }
    .mb-lg-n3 {
        margin-bottom: -1rem !important
    }
    .m-lg-n4 {
        margin: -1.5rem !important
    }
    .mb-lg-n4 {
        margin-bottom: -1.5rem !important
    }
    .m-lg-n5 {
        margin: -3rem !important
    }
    .mb-lg-n5 {
        margin-bottom: -3rem !important
    }
    .m-lg-auto {
        margin: auto !important
    }
    .mb-lg-auto {
        margin-bottom: auto !important
    }
}

.text-left {
    text-align: left !important
}

.text-right {
    text-align: right !important
}

.text-center {
    text-align: center !important
}

@media (min-width:992px) {
    .text-lg-left {
        text-align: left !important
    }
    .text-lg-right {
        text-align: right !important
    }
    .text-lg-center {
        text-align: center !important
    }
}

.font-weight-bold {
    font-weight: 700 !important
}

.text-white {
    color: #fff !important
}

.text-primary {
    color: #505879 !important
}

a.text-primary:focus,
a.text-primary:hover {
    color: #b33200 !important
}

.text-body {
    color: #757575 !important
}

.text-black-50 {
    color: rgba(0, 0, 0, .5) !important
}

.text-white-50 {
    color: rgba(255, 255, 255, .5) !important
}

.text-decoration-none {
    text-decoration: none !important
}

@media print {
    *,
    ::after,
    ::before {
        text-shadow: none !important;
        box-shadow: none !important
    }
    a:not(.btn) {
        text-decoration: underline
    }
    img {
        page-break-inside: avoid
    }
    h2,
    h3,
    p {
        orphans: 3;
        widows: 3
    }
    h2,
    h3 {
        page-break-after: avoid
    }
    @page {
        size: a3
    }
    body {
        min-width: 992px !important
    }
    .container {
        min-width: 992px !important
    }
    .navbar {
        display: none
    }
}

.font-weight-black {
    font-weight: 900 !important
}

h1 {
    font-weight: 800 !important
}

.font-weight-bold,
h2,
h3 {
    font-weight: 700 !important
}

h4 {
    font-weight: 600 !important
}

h6 {
    font-weight: 500 !important
}

p {
    font-weight: 400 !important
}

@keyframes action {
    0% {
        transform: translateY(0)
    }
    100% {
        transform: translateY(-15px)
    }
}

@keyframes pulse-border {
    0% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1);
        opacity: 1
    }
    100% {
        transform: translateX(-50%) translateY(-50%) translateZ(0) scale(1.5);
        opacity: 0
    }
}

div.gallery {
    margin: 5px;
    border: 1px solid #ccc;
    float: left;
    width: 180px
}

div.gallery:hover {
    border: 1px solid #777
}

div.gallery img {
    width: 100%;
    height: auto
}

.image-gallery {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px
}

.image-gallery>li {
    flex-basis: 350px;
    list-style-type: none
}

.image-gallery li img {
    object-fit: cover;
    max-width: 100%;
    height: auto;
    vertical-align: middle;
    border-radius: 5px
}