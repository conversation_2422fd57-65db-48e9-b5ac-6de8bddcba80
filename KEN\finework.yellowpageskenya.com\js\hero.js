document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    const indicators = document.querySelectorAll('.slider-dot');
    const prevBtn = document.querySelector('.arrow.prev');
    const nextBtn = document.querySelector('.arrow.next');
    let currentSlide = 0;
    let slideInterval;

    // Show specific slide by index
    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.classList.toggle('active', i === index);
            indicators[i].classList.toggle('active', i === index);
        });
        currentSlide = index;
    }

    // Next slide
    function nextSlide() {
        let next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }

    // Previous slide
    function prevSlide() {
        let prev = (currentSlide - 1 + slides.length) % slides.length;
        showSlide(prev);
    }

    // Start auto slide
    function startSlideShow() {
        slideInterval = setInterval(nextSlide, 9000); // 5 seconds
    }

    // Stop auto slide
    function stopSlideShow() {
        clearInterval(slideInterval);
    }

    // Dot navigation
    indicators.forEach(indicator => {
        indicator.addEventListener('click', function() {
            stopSlideShow();
            showSlide(parseInt(this.dataset.index));
            startSlideShow();
        });
    });

    // Prev/Next buttons
    prevBtn.addEventListener('click', function() {
        stopSlideShow();
        prevSlide();
        startSlideShow();
    });

    nextBtn.addEventListener('click', function() {
        stopSlideShow();
        nextSlide();
        startSlideShow();
    });

    // Init first slide
    showSlide(currentSlide);
    startSlideShow();
});
