<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>Terryann Collection :Bold Looks, Timeless Style</title>
  <meta name="description"
    content="Elevate your style with <PERSON><PERSON> Collection—designer wear, bridal gowns, African fashion & custom designs made just for you">


  <meta name="keywords" content="Terryann Collection">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-08-13">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
<meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
 
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://terryanncollection.yellowpageskenya.com">
  <meta property="og:title" content="Terryann Collection :Bold Looks, Timeless Style">
  <meta property="og:description"
    content="Elevate your style with Terryann Collection—designer wear, bridal gowns, African fashion & custom designs made just for you">
  <meta property="og:image" content="https://terryanncollection.yellowpageskenya.com/img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://terryanncollection.yellowpageskenya.com ">
  <meta property="twitter:title" content="Terryann Collection :Bold Looks, Timeless Style ">
  <meta property="twitter:description"
    content="Elevate your style with Terryann Collection—designer wear, bridal gowns, African fashion & custom designs made just for you">
  <meta property="twitter:image" content="https://terryanncollection.yellowpageskenya.com/img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://terryanncollection.yellowpageskenya.com">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://terryanncollection.yellowpageskenya.com">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://terryanncollection.yellowpageskenya.com/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>


  <link rel="preload" as="image" href="./img/slider_1.webp" fetchpriority="high">
   <link rel="preload" as="image" href="./img/slider_2.webp" fetchpriority="high">
    <link rel="preload" as="image" href="./img/slider_3.webp" fetchpriority="high">
    
    <link rel="preload" as="image" href="./img/hero_1.webp" fetchpriority="high">
    <link rel="preload" as="image" href="./img/hero_2.webp" fetchpriority="high">
       <link rel="preload" as="image" href="./img/hero_3.webp" fetchpriority="high">
  


  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hiro.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <link rel="stylesheet" href="css/categories.css">
   


  <!-- <link rel="alternate" hreflang="en" href="https://terryanncollection.yellowpageskenya.com/"> -->
  <link rel="alternate" hreflang="x-default" href="https://terryanncollection.yellowpageskenya.com/">


 

   <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-CWTT6XGH2S"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-CWTT6XGH2S');
</script>


  

  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

<script type="application/ld+json">

  {
  "document": {
    "type": "html",
    "lang": "en",
    "head": {
      "meta": [
        {
          "charset": "utf-8"
        },
        {
          "name": "description",
          "content": "Elevate your style with Terryann Collection—designer wear, bridal gowns, African fashion & custom designs made just for you"
        },
        {
          "name": "keywords",
          "content": "Terryann Collection"
        },
        {
          "name": "viewport",
          "content": "width=device-width, initial-scale=1.0"
        },
        {
          "name": "robots",
          "content": "index, follow"
        },
        {
          "property": "article:published_time",
          "content": "2025-08-13"
        },
        {
          "name": "google-site-verification",
          "content": "N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ"
        }
      ],
      "openGraph": {
        "type": "website",
        "url": "https://terryanncollection.yellowpageskenya.com",
        "title": "Terryann Collection :Bold Looks, Timeless Style",
        "description": "Elevate your style with Terryann Collection—designer wear, bridal gowns, African fashion & custom designs made just for you",
        "image": "https://terryanncollection.yellowpageskenya.com/img/logo.webp"
      },
      "twitter": {
        "card": "summary_large_image",
        "site": "@yellowpages254",
        "url": "https://terryanncollection.yellowpageskenya.com",
        "title": "Terryann Collection :Bold Looks, Timeless Style",
        "description": "Elevate your style with Terryann Collection—designer wear, bridal gowns, African fashion & custom designs made just for you",
        "image": "https://terryanncollection.yellowpageskenya.com/img/logo.webp"
      },
      "links": [
        {
          "rel": "icon",
          "href": "./favicon/favicon.ico"
        },
        {
          "rel": "apple-touch-icon",
          "sizes": "180x180",
          "href": "./favicon/apple-touch-icon.jpg"
        },
        {
          "rel": "icon",
          "type": "image/jpg",
          "sizes": "32x32",
          "href": "./favicon/favicon-32x32.jpg"
        },
        {
          "rel": "icon",
          "type": "image/jpg",
          "sizes": "16x16",
          "href": "./favicon/favicon-16x16.jpg"
        },
        {
          "rel": "manifest",
          "href": "./favicon/site.webmanifest.json"
        },
        {
          "rel": "canonical",
          "href": "https://terryanncollection.yellowpageskenya.com"
        },
        {
          "rel": "alternate",
          "hreflang": "en",
          "href": "https://terryanncollection.yellowpageskenya.com"
        },
        {
          "rel": "alternate",
          "hreflang": "x-default",
          "href": "https://terryanncollection.yellowpageskenya.com/"
        },
        {
          "rel": "sitemap",
          "type": "application/xml",
          "title": "Sitemap",
          "href": "https://terryanncollection.yellowpageskenya.com/sitemap.xml"
        },
        {
          "rel": "preconnect",
          "href": "https://maps.googleapis.com",
          "crossorigin": true
        },
        {
          "rel": "preconnect",
          "href": "https://maps.gstatic.com",
          "crossorigin": true
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/slider_1.webp",
          "fetchpriority": "high"
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/slider_2.webp",
          "fetchpriority": "high"
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/slider_3.webp",
          "fetchpriority": "high"
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/hero_1.webp",
          "fetchpriority": "high"
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/hero_2.webp",
          "fetchpriority": "high"
        },
        {
          "rel": "preload",
          "as": "image",
          "href": "./img/hero_3.webp",
          "fetchpriority": "high"
        }
      ],
      "stylesheets": [
        "css/features.css",
        "css/ots.css",
        "css/s2.css",
        "css/hiro.css",
        "css/service-section.css",
        "css/mn.css",
        "css/about.css",
        "css/main.css",
        "css/services.css",
        "css/testimonial.css",
        "css/categories.css"
      ],
      "scripts": [
        {
          "src": "https://www.googletagmanager.com/gtag/js?id=G-CWTT6XGH2S",
          "async": true
        }
      ],
      "inlineStyle": "Font face definitions for Poppins and Work Sans, body styling, and mobile menu transitions",
      "inlineScript": "Google Analytics configuration and current year script"
    },
    "body": {
      "sections": [
        {
          "type": "navigation",
          "id": "main-nav",
          "class": "content-grid",
          "content": {
            "logo": {
              "src": "./img/logo.webp",
              "alt": "Logo",
              "width": "86",
              "height": "70"
            },
            "menuItems": [
              {
                "text": "Home",
                "href": "/"
              },
              {
                "text": "About Us",
                "href": "#about"
              },
              {
                "text": "Services",
                "href": "#services"
              },
              {
                "text": "Contact",
                "href": "#contact"
              }
            ],
            "contactButton": {
              "text": "Get In Touch",
              "href": "mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
            }
          }
        },
        {
          "type": "mobileMenu",
          "id": "mobile-menu",
          "class": "mobile-menu",
          "content": {
            "logo": {
              "src": "./img/logo.webp",
              "alt": "Logo",
              "width": "86",
              "height": "70"
            },
            "menuItems": [
              {
                "text": "Home",
                "href": "/"
              },
              {
                "text": "About Us",
                "href": "#about"
              },
              {
                "text": "Services",
                "href": "#services"
              },
              {
                "text": "Contact",
                "href": "#contact"
              }
            ],
            "contactButton": {
              "text": "Get In Touch",
              "href": "mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"
            }
          }
        },
        {
          "type": "hero",
          "class": "hero-container",
          "content": {
            "slider": {
              "slides": [
                {
                  "tag": "Top Trending",
                  "title": "Terryann Collection",
                  "tagline": "Where Culture Meets Class",
                  "description": "Discover our latest mix of African-inspired designs, elegant suits, and timeless outfits crafted to make every moment unforgettable.",
                  "image": "./img/slider_1.webp"
                },
                {
                  "tag": "New Arrivals",
                  "title": "Terryann Collection",
                  "tagline": "Your Style, Our Passion",
                  "description": "Step into the season's hottest looks—bridal gowns, Italian wear, vitenge pieces, and custom creations made just for you.",
                  "image": "./img/slider_2.webp"
                },
                {
                  "tag": "Limited Edition",
                  "title": "Terryann Collection",
                  "tagline": "Bold Looks, Timeless Style",
                  "description": "Own the spotlight with our exclusive fashion pieces designed for confidence, comfort, and pure sophistication.",
                  "image": "./img/slider_3.webp"
                }
              ]
            },
            "productGrid": {
              "mainImage": "./img/hero_1.webp",
              "smallImages": [
                "./img/hero_2.webp",
                "./img/hero_3.webp"
              ]
            }
          }
        },
        {
          "type": "features",
          "class": "features",
          "content": {
            "title": "Core Values",
            "description": "At Terryann Collection, our values shape every design creativity that blends culture with trends, quality you can trust, and affordability you deserve.",
            "cards": [
              {
                "title": "Vision",
                "description": "To be the leading fashion brand in Kenya and beyond.",
                "icon": "checkmark circle"
              },
              {
                "title": "Mission",
                "description": "Blending culture, comfort, and class to create fashion that empowers and expresses individuality.",
                "icon": "calendar"
              },
              {
                "title": "Core Values",
                "values": [
                  "Creativity",
                  "Quality",
                  "Affordability"
                ],
                "icon": "layers"
              }
            ]
          }
        },
        {
          "type": "about",
          "id": "about",
          "class": "about-us",
          "content": {
            "title": "About Us",
            "description": [
              "At Terryann Collection, fashion is more than just clothing – it's a celebration of creativity, comfort, and confidence. We blend timeless style with innovative design to offer high-quality, affordable outfits that suit every occasion and personality.",
              "From elegant designer wear and stylish suits for both men and women, to breathtaking bridal gowns, African-inspired fashion, Italian pieces, and vibrant vitenge outfits – we've got something special for everyone.",
              "Whether you're dressing for a wedding, a casual day out, or looking to refresh your wardrobe, we're here to help you look and feel your best. We also offer personalized fashion and design consultations to bring your unique style vision to life. Visit us today for all your fashion needs!"
            ],
            "services": [
              "Designer Clothes",
              "Ladies' Suits",
              "Men's Suits",
              "Wedding Gowns",
              "Italian Outfits",
              "Bridal Outfits",
              "Casual Outfits",
              "Vitenge Material Clothes"
            ],
            "images": [
              {
                "src": "./img/about_image_scale,w_435.webp",
                "alt": "terryann collection about image",
                "type": "main"
              },
              {
                "src": "./img/about_image_sm_scale,w_265.webp",
                "alt": "About image",
                "type": "secondary"
              }
            ]
          }
        },
        {
          "type": "products",
          "id": "products",
          "class": "top-categories",
          "content": {
            "title": "Our Collection",
            "categories": [
              {
                "name": "Designer Clothes",
                "image": "./img/Designer_Clothes.webp"
              },
              {
                "name": "Ladies' Suits",
                "image": "./img/Ladies_Suits.webp"
              },
              {
                "name": "Men's Suits",
                "image": "./img/Men_Suits.webp"
              },
              {
                "name": "Wedding Gowns",
                "image": "./img/Wedding_Gowns.webp"
              },
              {
                "name": "Bridal Outfits",
                "image": "./img/Bridal_Outfits.webp"
              },
              {
                "name": "Italian Outfits",
                "image": "./img/Italian_Outfits.webp"
              },
              {
                "name": "Casual Outfits",
                "image": "./img/Casual_Outfits.webp"
              },
              {
                "name": "Vitenge Material Clothes",
                "image": "./img/African_Clothes.webp"
              },
              {
                "name": "African Clothes",
                "image": "./img/Vitenge_Material_Clothes.webp"
              }
            ]
          }
        },
        {
          "type": "services",
          "id": "services",
          "class": "services-container",
          "content": {
            "title": "Services",
            "services": [
              {
                "name": "Custom Tailoring & Design",
                "image": "./img/Custom_Tailoring.webp",
                "description": "We bring your fashion ideas to life with made-to-measure outfits designed to fit perfectly and reflect your personality."
              },
              {
                "name": "Fashion & Style Consultation",
                "image": "./img/Fashion_Style_Consultation.webp",
                "description": "Need help finding your look? Our experienced team offers personalized style advice to help you dress confidently for any occasion.
"
              },
              {
                "name": "Bridal Styling & Fitting",
                "image": "./img/Bridal_Styling_Fitting.webp",
                "description": "From the wedding gown to bridal party outfits, we provide complete styling and fitting services to make your special day truly flawless."
              },
              {
                "name": "Group & Event Styling",
                "image": "./img/Group_Event_Styling.webp",
                "description": "We style individuals and groups for weddings, corporate events, photo shoots, and special occasions, ensuring everyone looks their best."
              },
              {
                "name": "Wardrobe Makeovers",
                "image": "./img/Wardrobe_Makeovers.webp",
                "description": "We help refresh and elevate your closet with versatile, stylish pieces that suit your lifestyle and body type."
              },
              {
                "name": "Trend Updates & Styling Tips",
                "image": "./img/Trend_Updates.webp",
                "description": "Stay ahead of fashion trends with our ongoing style insights, tips, and outfit recommendations."
              }
            ]
          }
        },
        {
          "type": "contact",
          "id": "contact",
          "class": "contact",
          "content": {
            "title": "Contact Us",
            "map": {
              "src": "https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.8176237512503!2d36.825614699999996!3d-1.2832797999999999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f11f2d9053293%3A0xc5ae762ae3740c57!2sTerryann%20Collection!5e0!3m2!1sen!2ske!4v1755167891907!5m2!1sen!2ske",
              "width": "600",
              "height": "450"
            },
            "contactInfo": [
              {
                "type": "address",
                "label": "Physical Location",
                "value": "Taveta Rd Intermark Building, 3rd floor"
              },
              {
                "type": "phone",
                "label": "Phone number",
                "value": "0729685877"
              },
              {
                "type": "email",
                "label": "Email",
                "value": "<EMAIL>"
              },
              {
                "type": "hours",
                "label": "Operating Hours",
                "value": "8:00am – 6:30pm"
              }
            ],
            "socialLinks": [
              {
                "platform": "Facebook",
                "url": "https://www.facebook.com/p/Terryann-collections-100063876457663/"
              },
              {
                "platform": "Instagram",
                "url": "https://www.instagram.com/terrykamene"
              },
              {
                "platform": "WhatsApp",
                "url": "https://api.whatsapp.com/send/?phone=0729685877&amp;text&amp;type=phone_number&amp;app_absent=0"
              }
            ]
          }
        },
        {
          "type": "footer",
          "class": "footer",
          "content": {
            "copyright": "© <span id=\"current-year\"></span> Terryann Collection. All Rights Reserved.",
            "poweredBy": {
              "name": "Yellow Pages Kenya",
              "url": "https://www.yellowpageskenya.com/",
              "logo": "./img/yp_logo.webp"
            }
          }
        }
      ],
      "scripts": [
        "./js/hero.js",
        "./js/testimonial.js",
        "Inline JavaScript for mobile menu, sticky header, parallax scrolling, lazy loading, and smooth scrolling"
      ]
    }
  }
}
</script>


</head>

<body>
  <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="86" height="70">
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">About Us</a>
                <a href="#services">Services</a>
                <a href="#contact">Contact</a>
            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Get In Touch</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="86" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                <a href="#about">About Us</a>
                <a href="#services">Services</a>
                <a href="#contact">Contact</a>
                <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya" class="contact-btn">Get In Touch</a>
            </div>
        </div>
    </div>

    <div class="hero-container">
        <!-- Enhanced Slider section -->
       <div class="content-grid">
        <div class="slider-wrapper breakout">
             <!-- Slider with enhanced controls -->
         <div class="slider-container">
             <div class="slider">
          <!-- Slide 1 -->
          <div class="slide">
              <div class="slide-inner active">
                  <div class="slide-content">
                      <div class="tag">Top Trending</div>
                      <h1 class="slide-title cyan">Terryann Collection</h1>
                      <p class="slide-tagline">"Where Culture Meets Class"</p>
                      <p class="slide-description">Discover our latest mix of African-inspired designs, elegant suits, and timeless outfits crafted to make every moment unforgettable.</p>
                      <div class="slide-buttons">
                          <a href="#products" class="discover-button">View Collection</a>
                          <a href="#services" class="services-button">Our Services</a>
                      </div>
                  </div>
                  <div class="slide-image-container">
                      <img class="slide-image" src="./img/slider_1.webp" alt="slider1" title="slider1">
                  </div>
              </div>
          </div>
          <!-- Slide 2 -->
          <div class="slide">
              <div class="slide-inner">
                  <div class="slide-content">
                      <div class="tag">New Arrivals</div>
                      <h1 class="slide-title">Terryann Collection</h1>
                      <p class="slide-tagline">"Your Style, Our Passion"</p>
                      <p class="slide-description">Step into the season’s hottest looks—bridal gowns, Italian wear, vitenge pieces, and custom creations made just for you.</p>
                      <div class="slide-buttons">
                          <a href="#products" class="discover-button">View Collection</a>
                          <a href="#services" class="services-button">Our Services</a>
                      </div>
                  </div>
                  <div class="slide-image-container">
                      <img class="slide-image" src="./img/slider_2.webp" alt="slider2" title="slider2">
                  </div>
              </div>
          </div>
          <!-- Slide 3 -->
          <div class="slide">
              <div class="slide-inner">
                  <div class="slide-content">
                      <div class="tag">Limited Edition</div>
                      <h1 class="slide-title blue">Terryann Collection</h1>
                      <p class="slide-tagline">"Bold Looks, Timeless Style"</p>
                      <p class="slide-description">Own the spotlight with our exclusive fashion pieces designed for confidence, comfort, and pure sophistication.</p>
                      <div class="slide-buttons">
                          <a href="#products" class="discover-button">View Collection</a>
                          <a href="#services" class="services-button">Our Services</a>
                      </div>
                  </div>
                  <div class="slide-image-container">
                      <img class="slide-image" src="./img/slider_3.webp" alt="slider3" title="slider3">
                  </div>
              </div>
          </div>
             </div>
         
             <!-- Enhanced Slider controls -->
             <div class="slider-controls">
          <div class="slider-dot active"></div>
          <div class="slider-dot"></div>
          <div class="slider-dot"></div>
             </div>
         
             <div class="slider-arrows">
          <div class="slider-arrow prev">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path d="M15 18L9 12L15 6" stroke="#333" stroke-width="2"
                      stroke-linecap="round" stroke-linejoin="round" />
              </svg>
          </div>
          <div class="slider-arrow next">
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <path d="M9 6L15 12L9 18" stroke="#333" stroke-width="2"
                      stroke-linecap="round" stroke-linejoin="round" />
              </svg>
          </div>
             </div>
         </div>

                <div class="product-grid">
    <div class="product-card">
       
        <div class="product-image" style="background-image: url('./img/hero_1.webp');"></div>
    </div>

    <div class="product-row">
        <div class="small-product">
           
            <div class="product-image" style="background-image: url('./img/hero_2.webp');"></div>
        </div>

        <div class="small-product">

            <div class="product-image" style="background-image: url('./img/hero_3.webp');"></div>
        </div>
    </div>
</div>
       </div>

        


    </div>

    </div>




  <section class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        <div class="text-content">
          <h2 class="animate-text">Core Values</h2>
          <p class="animate-text-delay">At Terryann Collection, our values shape every design creativity that blends culture with trends, quality you can trust, and affordability you deserve.</p>
        </div>
        <div class="cards-container">
        
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24"  viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                </svg>
  
              </div>
              <h3>
Vision
</h3>
            </div>

            <p>
To be the leading fashion brand in Kenya and beyond.
</p>

            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z" />
                </svg>
              </div>
              <h3>Mission
</h3>
            </div>

            <p>Blending culture, comfort, and class to create fashion that empowers and expresses individuality.
</p>
            
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt>
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3" />
                </svg>
              </div>
              <h3>Core Values
</h3>
            </div>

            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
                Creativity
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
              Quality
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg> Affordability
 </span> 
              </li>
                
                


              <li>
              

               </li>
  
             
             
            </ul>
           
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </section>




    <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/about_image_scale,w_340.webp"
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp" 
    srcset="./img/about_image_scale,w_435.webp"
  >
  <img
    src="./img/about_image_scale,w_435.webp"
    alt="terryann collection about image"
    loading="lazy"
   
  >
</picture>

  </div>

  <div class="image-container bottom-right-image">
  

    <picture>
      <source srcset="
          ./img/about_image_sm_scale,w_200.webp 200w,
          ./img/about_image_sm_scale,w_265.webp 265w" sizes="(max-width: 265px) 100vw, 265px">
      <img src="./img/about_image_sm_scale,w_265.webp" alt="About image" loading="lazy">
    </picture>
  </div>
</div>

                <div class="about-text">
                    <span class="abt">About Us</span>
                    
                    <p>
                    At Terryann Collection, fashion is more than just clothing – it's a celebration of creativity, comfort, and confidence. We blend timeless style with innovative design to offer high-quality, affordable outfits that suit every occasion and personality.

                    </p>

                    <p>
                    From elegant designer wear and stylish suits for both men and women, to breathtaking bridal gowns, African-inspired fashion, Italian pieces, and vibrant vitenge outfits – we’ve got something special for everyone.

      
                    </p>

                    <p>
                    Whether you're dressing for a wedding, a casual day out, or looking to refresh your wardrobe, we’re here to help you look and feel your best. We also offer personalized fashion and design consultations to bring your unique style vision to life. Visit us today for all your fashion needs!
                    </p>

                  

                   

                    <div class="about-services-container">
                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
      Designer Clothes 

                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
                      Ladies' Suits 

                            </li>

                             <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
                     Men’s Suits 

                            </li>


                             <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
                       Wedding Gowns 

                            </li>

                           
                            

                        </ul>

                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
                     Italian Outfits 
                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
             Bridal Outfits 
                            </li>
                            

                              <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
     Casual Outfits 
                            </li>

                            
                              <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z" />
                                    </svg>
                                </span>
Vitenge Material Clothes 
 
                            </li>

                          
                            
                        </ul>
                    </div>

                    <a href="#services" class="cta-button">View More</a>
                </div>
            </div>
        </div>
    </div>

         <section class="top-categories" id="products">
        <div class="content-grid">
            <div class="section-header">
                
                <h2 class="cat-section-title">Our Collection
</h2>
             
            </div>

            
            
            <div class="categories-grid">
                <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Designer_Clothes.webp" alt="Designer Clothes" title="Designer Clothes" loading="lazy">

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Designer Clothes  </h3>
                    </div>
                </div>

                 <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Ladies_Suits.webp" alt="Ladies' Suits" title="Ladies' Suits" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Ladies' Suits</h3>
                    </div>
                </div>
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Men_Suits.webp" alt="Men’s Suits" title="Men’s Suits" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Men’s Suits</h3>
                    </div>
                </div>
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Wedding_Gowns.webp" alt="Wedding Gowns"  title="Wedding Gowns" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Wedding Gowns </h3>
                    </div>
                </div>
                
               
                
                <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Bridal_Outfits.webp" alt="Bridal Outfits"   title="Bridal Outfits" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Bridal Outfits  </h3>
                    </div>
                </div>
                 <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Italian_Outfits.webp" alt="Italian Outfits"   title="Italian Outfits" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Italian Outfits 
</h3>
                    </div>
                </div>

                 <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Casual_Outfits.webp" alt="Casual Outfits"   title="Casual Outfits" loading="lazy" >

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Casual Outfits</h3>
                    </div>
                </div>

                 <div class="category-card">
                    <div class="category-image">
                        <img src="./img/African_Clothes.webp" alt="Vitenge Material Clothes"   title="Vitenge Material Clothes" loading="lazy">

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">Vitenge Material Clothes</h3>
                    </div>
                </div>

                <div class="category-card">
                    <div class="category-image">
                        <img src="./img/Vitenge_Material_Clothes.webp" alt="African Clothes"   title="African Clothes" loading="lazy">

                    </div>
                    <div class="category-text-overlay">
                        <h3 class="category-title">African Clothes </h3>
                    </div>
                </div>
            </div>
        </div>
    </section>


    <div class="services-container" id="services">
      
  <div class="content-grid">
      <div class="services-header">
        <h2 class="products-section-title">Services</h2>
    
      </div>
      <div class="services-grid">
        <div class="service-item">
          <div class="service-image">
            <img src="./img/Custom_Tailoring.webp" alt="Custom Tailoring"
              title="Custom Tailoring" loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Custom Tailoring & Design 
    </h3>

    <p>We bring your fashion ideas to life with made-to-measure outfits designed to fit perfectly and reflect your personality.
</p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img src="./img/Fashion_Style_Consultation.webp" alt="Fashion & Style Consultation"
              title="Fashion & Style Consultation" loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Fashion & Style Consultation
</h3>
<p>Need help finding your look? Our experienced team offers personalized style advice to help you dress confidently for any occasion.
</p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img src="./img/Bridal_Styling_Fitting.webp" alt="Bridal Styling & Fitting"
              title="Bridal Styling & Fitting" loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Bridal Styling & Fitting 
    </h3>
    <p>From the wedding gown to bridal party outfits, we provide complete styling and fitting services to make your special day truly flawless.
</p>
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img src="./img/Group_Event_Styling.webp" alt="Group & Event Styling"
              title="Group & Event Styling" loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Group & Event Styling
    </h3>

    <p>We style individuals and groups for weddings, corporate events, photo shoots, and special occasions, ensuring everyone looks their best.
</p>
    
          </div>
        </div>
        <div class="service-item">
          <div class="service-image">
            <img src="./img/Wardrobe_Makeovers.webp"
              alt="Wardrobe Makeovers" title="Wardrobe Makeovers"
              loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Wardrobe Makeovers</h3>
            <p>We help refresh and elevate your closet with versatile, stylish pieces that suit your lifestyle and body type.
</p>
    
    
          </div>
    
        </div>

           <div class="service-item">
          <div class="service-image">
            <img src="./img/Trend_Updates.webp"
              alt="Trend Updates & Styling Tips" title="Trend Updates & Styling Tips"
              loading="lazy" width="420" height="315">
          </div>
          <div class="service-content">
            <h3>Trend Updates & Styling Tips
  </h3>

  <p>Stay ahead of fashion trends with our ongoing style insights, tips, and outfit recommendations.
</p>
    
    
          </div>
    
        </div>
    
    
      </div>
    </div>
  </div>






  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contact Us</h2>
      <div class="contact-content">
        <div class="contact-map">

  

          <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3988.8176237512503!2d36.825614699999996!3d-1.2832797999999999!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x182f11f2d9053293%3A0xc5ae762ae3740c57!2sTerryann%20Collection!5e0!3m2!1sen!2ske!4v1755167891907!5m2!1sen!2ske" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"  title="Terryann Collection Map"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Physical Location</h3>
              <p>Taveta Rd Intermark Building, 3rd floor 



</p>

            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Phone number</h3>
              <p><a href="0729685877">0729685877
</a></p>
          
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Kenya"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Operating Hours</h3>
              <p>8:00am – 6:30pm
 
              </p>

             
            </div>
          </div>
          <!-- Social Media Icons -->

          <div class="contact-item">


            <span><svg class="contact-icon" xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem"
                viewBox="0 0 16 16">
                <path fill="currentColor"
                  d="M12 10c-.8 0-1.4.3-2 .8L6.8 9c.1-.3.2-.7.2-1s-.1-.7-.2-1L10 5.2c.6.5 1.2.8 2 .8c1.7 0 3-1.3 3-3s-1.3-3-3-3s-3 1.3-3 3v.5L5.5 5.4C5.1 5.2 4.6 5 4 5C2.4 5 1 6.3 1 8c0 1.6 1.4 3 3 3c.6 0 1.1-.2 1.5-.4L9 12.5v.5c0 1.7 1.3 3 3 3s3-1.3 3-3s-1.3-3-3-3" />
              </svg></span>

            <div>
              <h3>Connect With Us</h3>


              <div class="social-links">
              
             
                <!-- Facebook -->
                <a href="https://www.facebook.com/p/Terryann-collections-100063876457663/" target="_blank"
                  class="social-link" aria-label="Facebook">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor"
                      d="m16.117 8.906l-2.589 3.086l-3.715-2.281a.5.5 0 0 0-.644.104l-3.052 3.636a.5.5 0 0 0 .766.643l2.774-3.306l3.715 2.281c.211.13.485.085.645-.104l2.866-3.416a.5.5 0 0 0-.766-.643M12 1C5.715 1 .975 5.594.975 11.686a10.43 10.43 0 0 0 3.471 7.905c.071.06.114.149.118.242l.058 1.867a1.343 1.343 0 0 0 1.883 1.187l2.088-.92a.33.33 0 0 1 .226-.018c1.037.283 2.107.425 3.181.422c6.285 0 11.025-4.594 11.025-10.685S18.285 1 12 1m0 20.371a11 11 0 0 1-2.916-.387a1.36 1.36 0 0 0-.894.068l-2.086.919a.35.35 0 0 1-.324-.026a.34.34 0 0 1-.158-.276l-.058-1.871a1.34 1.34 0 0 0-.45-.952a9.45 9.45 0 0 1-3.14-7.16C1.975 6.164 6.285 2 12 2s10.025 4.164 10.025 9.686S17.715 21.37 12 21.37" />
                  </svg>
                </a>

                  <!--Instagram-->

                  <a href="https://www.instagram.com/terrykamene" target="_blank" class="social-link" aria-label="Instagram">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <path fill="currentColor" d="M16 7a1 1 0 1 1 2 0a1 1 0 0 1-2 0"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M12 7.25a4.75 4.75 0 1 0 0 9.5a4.75 4.75 0 0 0 0-9.5M8.75 12a3.25 3.25 0 1 1 6.5 0a3.25 3.25 0 0 1-6.5 0" clip-rule="evenodd"></path>
                    <path fill="currentColor" fill-rule="evenodd" d="M17.258 2.833a47.7 47.7 0 0 0-10.516 0c-2.012.225-3.637 1.81-3.873 3.832a46 46 0 0 0 0 10.67c.236 2.022 1.86 3.607 3.873 3.832a47.8 47.8 0 0 0 10.516 0c2.012-.225 3.637-1.81 3.873-3.832a46 46 0 0 0 0-10.67c-.236-2.022-1.86-3.607-3.873-3.832m-10.35 1.49a46.2 46.2 0 0 1 10.184 0c1.33.15 2.395 1.199 2.55 2.517a44.4 44.4 0 0 1 0 10.32a2.89 2.89 0 0 1-2.55 2.516a46.2 46.2 0 0 1-10.184 0a2.89 2.89 0 0 1-2.55-2.516a44.4 44.4 0 0 1 0-10.32a2.89 2.89 0 0 1 2.55-2.516" clip-rule="evenodd"></path>
                  </svg>
                </a>
               
           
                 <!-- Whatsapp -->
               <a href="https://api.whatsapp.com/send/?phone=0729685877&amp;text&amp;type=phone_number&amp;app_absent=0" target="_blank" class="social-link" aria-label="Whatsapp">
                  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24">
                    <g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" color="currentColor">
                      <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2S2 6.477 2 12c0 1.379.28 2.693.784 3.888c.279.66.418.99.436 1.24c.017.25-.057.524-.204 1.073L2 22l3.799-1.016c.549-.147.823-.22 1.073-.204c.************* 1.24.436A10 10 0 0 0 12 22"></path>
                      <path d="m8.588 12.377l.871-1.081c.367-.456.82-.88.857-1.488c.008-.153-.1-.841-.315-2.218C9.916 7.049 9.41 7 8.973 7c-.57 0-.855 0-1.138.13c-.358.163-.725.622-.806 1.007c-.064.305-.016.515.079.935c.402 1.783 1.347 3.544 2.811 5.009c1.465 1.464 3.226 2.409 5.01 2.811c.42.095.629.143.934.079c.385-.08.844-.448 1.008-.806c.129-.283.129-.568.129-1.138c0-.438-.049-.943-.59-1.028c-1.377-.216-2.065-.323-2.218-.315c-.607.036-1.032.49-1.488.857l-1.081.87"></path>
                    </g>
                  </svg>
                </a>
              
               
              </div>
            </div>
          </div>
          
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy; <span id="current-year"></span> Terryann Collection. All Rights Reserved.</p>

        </div>
        <div class="designer">
          <a href="https://www.yellowpageskenya.com/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50"
              title="Yellow Pages Kenya">
            <p>Powered by Yellow Pages Kenya.</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script src="./js/hero.js"></script>
  <script src="./js/testimonial.js"></script>

  <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
<script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

    <script>
        // Add smooth scrolling for CTA buttons
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Add intersection observer for animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe elements for scroll animations
        document.querySelectorAll('.image-circle').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(50px)';
            el.style.transition = 'all 0.8s ease';
            observer.observe(el);
        });
    </script>

 


</body>

</html>