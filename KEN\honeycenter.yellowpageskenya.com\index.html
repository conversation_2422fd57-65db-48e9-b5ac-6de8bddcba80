<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>Honey Center: Pure, Natural Flavor Experience</title>
    <meta name="description"
        content="Experience rich and natural flavor of pure honey with Honey Center's Wide Range of Products and Services! We are not just any ordinary honey business - we take pride in bringing you a variety of delicious and nutritious honey products that are sure to satisfy your taste buds.">
    <meta name="keywords"
        content="Honey Center, Honey Company, Where to sell honey in bulk in Kenya, Where to buy honey in Kenya, Honey companies in Kenya, Honey business in Kenya, Companies that buy honey in Kenya, Companies selling honey in Nairobi, Honey business, Where to get honey in Kenya, Nearest honey farm, Honey business Kenya">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
    <!-- Favicon -->
    <link rel="icon" href="./favicon/favicon.ico">
    <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
    <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
    <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
    <link rel="manifest" href="./favicon/site.webmanifest.json">

 

    <!-- Customized Bootstrap Stylesheet -->
    <link href="css/style.css" rel="stylesheet">

    

<style>
        /* Inline critical font styles for Poppins */
        @font-face {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 400;
          src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
          font-display: swap;
        }
    
        @font-face {
          font-family: 'Poppins';
          font-style: normal;
          font-weight: 700;
          src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
          font-display: swap;
        }

        .icon {
    fill: currentColor;
    vertical-align: middle;
}

</style>



</head>

<body>
    <!-- Topbar Start -->

<svg style="display: none;">
    <symbol id="phone-icon" viewBox="0 0 24 24">
        <path d="M20 15.5c-1.2 0-2.4-.2-3.6-.6-.3-.1-.7 0-1 .2l-2.2 2.2c-2.8-1.4-5.1-3.8-6.6-6.6l2.2-2.2c.3-.3.4-.7.2-1-.3-1.1-.5-2.3-.5-3.5 0-.6-.4-1-1-1H4c-.6 0-1 .4-1 1 0 9.4 7.6 17 17 17 .6 0 1-.4 1-1v-3.5c0-.6-.4-1-1-1z"/>
    </symbol>
    
    <symbol id="email-icon" viewBox="0 0 24 24">
        <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
    </symbol>
</svg>




<div class="container-fluid bg-dark">
    <div class="row py-2 px-lg-5">
        <div class="col-lg-6 text-center text-lg-left mb-2 mb-lg-0">
            <div class="d-inline-flex align-items-center text-white">
                <small>
                    <svg class="icon mr-2" width="16" height="16">
                        <use href="#phone-icon"/>
                    </svg>
                    +************ / 0715611720
                </small>
                <small class="px-3">|</small>
                <small>
                    <svg class="icon mr-2" width="16" height="16">
                        <use href="#email-icon"/>
                    </svg>
                    <EMAIL>
                </small>
            </div>
        </div>
        <div class="col-lg-6 text-center text-lg-right">
            <div class="d-inline-flex align-items-center">
            </div>
        </div>
    </div>
</div>
    <!-- Topbar End -->


    <!-- Navbar Start -->
    <div class="container-fluid p-0">
        <nav class="navbar navbar-expand-lg bg-light navbar-light py-3 py-lg-0 px-lg-5">
            <a href="/" class="navbar-brand ml-lg-3">
                <h1 class="m-0 display-5 text-uppercase text-primary">
                    <img class="img-fluid w-100" src="img/logo.webp" alt="Honey Centerlogo"></i>
                </h1>
            </a>
            <button type="button" class="navbar-toggler" data-toggle="collapse" data-target="#navbarCollapse" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse justify-content-between px-lg-3" id="navbarCollapse">
                <div class="navbar-nav m-auto py-0">
                    <a href="/" class="nav-item nav-link active">Home</a>
                    <a href="#about" class="nav-item nav-link">About</a>
                    <a href="#services" class="nav-item nav-link">Service</a>
                    <a href="#whyus" class="nav-item nav-link">Why Us</a>
                    <a href="#contact" class="nav-item nav-link">Contact Us</a>
                </div>
                <a href="#contact" class="btn btn-primary py-2 px-4 d-none d-lg-block">Get A Quote</a>
            </div>
        </nav>
    </div>
    <!-- Navbar End -->


    <!-- Header Start -->
    <div class="jumbotron jumbotron-fluid mb-5">
        <div class="container text-center py-5">

            <h1 class="text-white display-3 mb-5">Honey Center</h1>
        </div>
    </div>
    <!-- Header End -->


    <!-- About Start -->
    <div class="container-fluid py-5" id="about">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-5 pb-4 pb-lg-0">
                    <img class="img-fluid w-100" src="img/about.webp" alt="">
                    <div class="bg-primary text-dark text-center p-4">
                        <h1 style="color: white;" class="m-0">The Best in Kenya</h1>
                    </div>
                </div>
                <div class="col-lg-7">
                    <span class="text-primary text-uppercase font-weight-bold">Who We Are</span>
                    <h1 class="mb-4">About Us</h1>
                    <p class="mb-4">Experience rich and natural flavor of pure honey with Honey Center's Wide Range of
                        Products and Services! We are not just any ordinary honey business - we take pride in bringing
                        you a variety of delicious and nutritious honey products that are sure to satisfy your taste
                        buds.

                        From raw and organic honey to handcrafted honeycomb, we have everything you need to enjoy the
                        rich and natural flavor of pure honey. Our products are carefully sourced from the finest
                        beekeepers and farms, ensuring that you only get the best quality honey that is free from
                        additives and preservatives. And if you have a sweet tooth, you'll love our honey-infused treats
                        such as honey sticks, honey butter, and honey candy.

                        With our exceptional services, we make sure that your honey reaches you in the freshest and most
                        convenient way possible. We supply and distribute our products to various locations, so you can
                        enjoy the goodness of honey from where you are.

                        Experience the benefits of pure honey today, and let Honey Center be your go-to source for all
                        your honey needs!</p>

                </div>
            </div>
        </div>
        <!-- Video Modal -->
        <div class="modal fade" id="videoModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-body">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                        <!-- 16:9 aspect ratio -->
                        <div class="embed-responsive embed-responsive-16by9">
                            <iframe class="embed-responsive-item" src="" id="video" allowscriptaccess="always"
                                allow="autoplay"></iframe>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- About End -->


    <!-- Services Start -->
    <div class="container-fluid pt-5" id="services">
        <div class="container">
            <div class="text-center pb-2">
                <span class="text-primary text-uppercase font-weight-bold">The Best Products Available</span>
                <h1 class="mb-4">Our Products</h1>
            </div>
            <div class="row pb-3">

                <div class="col-lg-4 col-md-6 text-center mb-5">
                    <div class="d-flex align-items-center justify-content-center bg-primary mb-4 p-4">

                        <span class="text-white font-weight-medium m-0">Sweeteners </span>
                    </div>
                    <p>Our sweeteners are exquisitely crafted and fashioned into cute and whimsical designs to make you
                        smile.</p>

                </div>
                <div class="col-lg-4 col-md-6 text-center mb-5">
                    <div class="d-flex align-items-center justify-content-center bg-primary mb-4 p-4">

                        <span class="text-white font-weight-medium m-0">Health supplements </span>
                    </div>
                    <p>Our range of honey products is not only delicious but also packed with essential nutrients and
                        antioxidants, to boost your immune system, improve your digestion, and promote your overall
                        wellbeing.</p>

                </div>
                <div class="col-lg-4 col-md-6 text-center mb-5">
                    <div class="d-flex align-items-center justify-content-center bg-primary mb-4 p-4">

                        <span class="text-white font-weight-medium m-0">Skincare solution </span>
                    </div>
                    <p>Our honey-based skincare products will help nourish and hydrate your skin. </p>

                </div>
            </div>




        </div>
    </div>
    <!-- Services End -->


    <!-- Features Start -->
    <div class="container-fluid bg-secondary my-5" id="whyus">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-5">
                    <img class="img-fluid w-100" src="img/feature.webp" alt="">
                </div>
                <div class="col-lg-7 py-5 py-lg-0">
                    <span class="text-primary text-uppercase font-weight-bold">Why Choose Us</span>
                    <h1 class="mb-4">Our Core Values</h1>
                    <ul class="list-inline">
                        <li>
                            <span><i class="far fa-dot-circle text-primary mr-3"></i>Integrity & honesty</span>
                        <li>
                            <span><i class="far fa-dot-circle text-primary mr-3"></i>Commitment to customers</span>
                        </li>
                        <li>
                            <span><i class="far fa-dot-circle text-primary mr-3"></i>Quality</span>
                        </li>
                    </ul>
                    <a href="" class="btn btn-primary mt-3 py-2 px-4">Contact Us</a>
                </div>
            </div>
        </div>
    </div>
    <!-- Features End -->




    <!-- Footer Start -->
 
<svg style="display: none;">
    <symbol id="phone-icon" viewBox="0 0 24 24">
        <path d="M20 15.5c-1.2 0-2.4-.2-3.6-.6-.3-.1-.7 0-1 .2l-2.2 2.2c-2.8-1.4-5.1-3.8-6.6-6.6l2.2-2.2c.3-.3.4-.7.2-1-.3-1.1-.5-2.3-.5-3.5 0-.6-.4-1-1-1H4c-.6 0-1 .4-1 1 0 9.4 7.6 17 17 17 .6 0 1-.4 1-1v-3.5c0-.6-.4-1-1-1z"/>
    </symbol>
    
    <symbol id="email-icon" viewBox="0 0 24 24">
        <path d="M20 4H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 4l-8 5-8-5V6l8 5 8-5v2z"/>
    </symbol>

    <symbol id="location-icon" viewBox="0 0 24 24">
        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
    </symbol>
</svg>


<div class="bg-dark text-white mt-5 pt-5 px-sm-3 px-md-5" id="contact">
    <div class="container">
        <div class="row">
            <div class="col-md-12 mb-5 text-center">
                <h1 class="text-primary mb-4">Get In Touch</h1>
                <p>
                    <svg class="icon mr-2" width="16" height="16">
                        <use href="#phone-icon"/>
                    </svg>
                    <a style="color: white;" href="tel:+************">+************</a>
                </p>
                <p>
                    <svg class="icon mr-2" width="16" height="16">
                        <use href="#email-icon"/>
                    </svg>
                    <a style="color: white;" href="mailto:<EMAIL>"><EMAIL></a>
                </p>
                <p>
                    <svg class="icon mr-2" width="16" height="16">
                        <use href="#location-icon"/>
                    </svg>
                    <a style="color: white;" href="https://goo.gl/maps/1eamfLwjpDBXdjiD7">Tom Mboya Street, Platinum plaza</a>
                </p>
            </div>
        </div>
    </div>
</div>




    <footer class="text-center bg-dark">
        <div class="row">
            <div class="col-12" style="margin-top: 10px;">
                <p class="text-white">&copy; 2023 Honey Center. All Rights Reserved.</p>
            </div>
        </div>
        <div class="row">
            <div class="col-12">
                <a href="https://www.yellowpageskenya.com" style="text-decoration: none; color:#fff">
                    <img src="./img/yp_logo.webp"
                        alt="Yellow Pages Kenya" width="50" height="50">
                    <p class="mb-0 text-white pt-3"><small>Designed by Yellow Pages Kenya</small></p>
                </a>
            </div>
        </div>
    </footer>
    <!-- Footer End -->


    <!-- Back to Top -->
    <a href="# " class="btn btn-lg btn-primary back-to-top "><i class="fa fa-angle-double-up "></i></a>

    <!-- Google tag (gtag.js) -->
 
<script>
    function loadGTM() {
      if (window.gtmLoaded) return; // Prevent multiple loads
      window.gtmLoaded = true;
  
      var script = document.createElement("script");
      script.src = "https://www.googletagmanager.com/gtag/js?id=G-SBWGJEGREC";
      script.async = true;
      document.head.appendChild(script);
  
      window.dataLayer = window.dataLayer || [];
      function gtag(){ dataLayer.push(arguments); }
      gtag('js', new Date());
      gtag('config', 'G-SBWGJEGREC');
    }
  
    window.addEventListener("scroll", loadGTM, { once: true });
    window.addEventListener("mousemove", loadGTM, { once: true });
    window.addEventListener("touchstart", loadGTM, { once: true });
  </script>
  

<script>
    document.addEventListener('DOMContentLoaded', function() {
    const navbarToggler = document.querySelector('.navbar-toggler');
    const navbarCollapse = document.querySelector('.navbar-collapse');

    navbarToggler.addEventListener('click', function() {
        // Toggle the 'show' class on navbar-collapse
        navbarCollapse.classList.toggle('show');
        
        // Toggle aria-expanded attribute
        const isExpanded = navbarToggler.getAttribute('aria-expanded') === 'true';
        navbarToggler.setAttribute('aria-expanded', !isExpanded);
    });

    // Close navbar when clicking outside
    document.addEventListener('click', function(event) {
        const isClickInside = navbarCollapse.contains(event.target) || 
                            navbarToggler.contains(event.target);
        
        if (!isClickInside && navbarCollapse.classList.contains('show')) {
            navbarCollapse.classList.remove('show');
            navbarToggler.setAttribute('aria-expanded', 'false');
        }
    });

    // Close navbar when clicking on nav links (for better mobile UX)
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', () => {
            if (navbarCollapse.classList.contains('show')) {
                navbarCollapse.classList.remove('show');
                navbarToggler.setAttribute('aria-expanded', 'false');
            }
        });
    });
});

</script>

</body>

</html>