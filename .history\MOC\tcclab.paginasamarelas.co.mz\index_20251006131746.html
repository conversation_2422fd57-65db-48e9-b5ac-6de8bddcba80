<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <title>TCC Lab</title>
  <meta name="description"
    content="O TCC Lab é uma iniciativa sediada em Moçambique que revoluciona o processo de desenvolvimento de monografias, oferecendo orientação personalizada e acesso a recursos especializados para ajudar os alunos a terem sucesso.">
  <meta name="keywords" content="House Girls Village and Bureau, Nairobi, house help in Nairobi, Nanny in Nairobi ">

  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.jpg">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.jpg">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.jpg">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-09-25">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
  
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://tcclab.paginasamarelas.co.mz/">
  <meta property="og:title" content="House Girls Village and Bureau: Reliable Nannies and House Helps for Your Home">
  <meta property="og:description"
    content="Find trusted nannies and house helps in Nairobi. House Girls Village connects you with reliable, trained, and affordable domestic workers.">
  <meta property="og:image" content="https://tcclab.paginasamarelas.co.mz//img/logo.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://tcclab.paginasamarelas.co.mz/ ">
  <meta property="twitter:title" content="House Girls Village and Bureau: Reliable Nannies and House Helps for Your Home ">
  <meta property="twitter:description"
    content="Find trusted nannies and house helps in Nairobi. House Girls Village connects you with reliable, trained, and affordable domestic workers.">
  <meta property="twitter:image" content="https://tcclab.paginasamarelas.co.mz//img/logo.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://tcclab.paginasamarelas.co.mz/">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://tcclab.paginasamarelas.co.mz/">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://tcclab.paginasamarelas.co.mz//sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>


  <link rel="preload" as="image" href="./img/slider_1a_scale,w_1400.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_1303.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/slider_1a_scale,w_856.webp" fetchpriority="high">


  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <!-- <link rel="stylesheet" href="css/categories.css"> -->
   

<!-- 
  <link rel="alternate" hreflang="en" href="https://tcclab.paginasamarelas.co.mz//"> -->
  <link rel="alternate" hreflang="x-default" href="https://tcclab.paginasamarelas.co.mz//">




 <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id=G-CQT61CDRY5"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'G-CQT61CDRY5');
</script>



  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "https://tcclab.paginasamarelas.co.mz//#organization",
      "name": "House Girls Village and Bureau",
      "url": "https://tcclab.paginasamarelas.co.mz/",
      "logo": "https://tcclab.paginasamarelas.co.mz//img/logo.webp",
      "description": "Find trusted nannies and house helps in Nairobi. House Girls Village connects you with reliable, trained, and affordable domestic workers.",
      "contactPoint": {
        "@type": "ContactPoint",
        "telephone": "+254726699446",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Westlands Rd",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "sameAs": [
        "https://www.facebook.com/p/House-Girls-Village-and-Bureau-Nairobi-100089786383876/",
        "https://api.whatsapp.com/send/?phone=0726699446"
      ]
    },
    {
      "@type": "LocalBusiness",
      "@id": "https://tcclab.paginasamarelas.co.mz//#localbusiness",
      "name": "House Girls Village and Bureau",
      "image": "https://tcclab.paginasamarelas.co.mz//img/logo.webp",
      "url": "https://tcclab.paginasamarelas.co.mz/",
      "telephone": "+254726699446",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Westlands Rd",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "openingHours": "Mo-Sa 08:00-17:00",
      "priceRange": "$$",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-1.2695058000000001",
        "longitude": "36.8087903"
      }
    },
    {
      "@type": "WebSite",
      "@id": "https://tcclab.paginasamarelas.co.mz//#website",
      "url": "https://tcclab.paginasamarelas.co.mz/",
      "name": "House Girls Village and Bureau",
      "description": "Reliable Nannies and House Helps for Your Home",
      "publisher": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#organization"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://tcclab.paginasamarelas.co.mz//?s={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@type": "WebPage",
      "@id": "https://tcclab.paginasamarelas.co.mz//#webpage",
      "url": "https://tcclab.paginasamarelas.co.mz/",
      "name": "House Girls Village and Bureau: Reliable Nannies and House Helps for Your Home",
      "isPartOf": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#website"
      },
      "about": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#organization"
      },
      "datePublished": "2025-09-25",
      "description": "Find trusted nannies and house helps in Nairobi. House Girls Village connects you with reliable, trained, and affordable domestic workers.",
      "inLanguage": "en",
      "potentialAction": {
        "@type": "ReadAction",
        "target": [
          "https://tcclab.paginasamarelas.co.mz/"
        ]
      }
    },
    {
      "@type": "AboutPage",
      "@id": "https://tcclab.paginasamarelas.co.mz//#about",
      "url": "https://tcclab.paginasamarelas.co.mz//#about",
      "name": "About Us",
      "description": "Learn about House Girls Village and Bureau, Nairobi - your trusted partner for finding reliable domestic workers.",
      "isPartOf": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#website"
      },
      "mainEntity": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau",
        "description": "A domestic worker agency in Nairobi that connects families with trusted, trained, and dependable domestic workers.",
        "foundingDate": "2025-09-25",
        "serviceArea": {
          "@type": "Country",
          "name": "Kenya"
        },
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Domestic Worker Services",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Live-In Nannies"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Live-Out House Helps"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Part-Time Babysitters"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Elderly Care Support"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Specialized Domestic Workers"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Recruitment & Placement"
              }
            }
          ]
        },
        "knowsAbout": [
          "Domestic Worker Placement",
          "Nanny Services",
          "House Help Services",
          "Elderly Care",
          "Childcare Services",
          "Household Staff Recruitment"
        ],
        "values": [
          {
            "@type": "DefinedTerm",
            "name": "Professionalism",
            "description": "We maintain high standards of professionalism in all our services and interactions."
          },
          {
            "@type": "DefinedTerm",
            "name": "Care",
            "description": "We show genuine care for both families and domestic workers in our placement process."
          },
          {
            "@type": "DefinedTerm",
            "name": "Reliability",
            "description": "We provide reliable and trustworthy domestic workers to ensure peace of mind for families."
          }
        ]
      }
    },
    {
      "@type": "Service",
      "name": "Live-In Nannies",
      "description": "Full-time caregivers who stay in your home to provide round-the-clock support for your children and household.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Childcare Services",
      "category": "Domestic Services"
    },
    {
      "@type": "Service",
      "name": "Live-Out House Helps",
      "description": "Reliable domestic workers who come in daily to assist with cleaning, cooking, laundry, and other household chores.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Housekeeping Services",
      "category": "Domestic Services"
    },
    {
      "@type": "Service",
      "name": "Part-Time Babysitters",
      "description": "Flexible babysitting services for parents who need short-term or occasional child care.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Childcare Services",
      "category": "Domestic Services"
    },
    {
      "@type": "Service",
      "name": "Elderly Care Support",
      "description": "Compassionate helpers trained to assist seniors with daily routines, companionship, and light household duties.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Elderly Care Services",
      "category": "Domestic Services"
    },
    {
      "@type": "Service",
      "name": "Specialized Domestic Workers",
      "description": "Trained staff for specific needs such as cooking, cleaning, or childcare, depending on your family's requirements.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Specialized Domestic Services",
      "category": "Domestic Services"
    },
    {
      "@type": "Service",
      "name": "Recruitment & Placement",
      "description": "A stress-free hiring process with thorough background checks, reference verification, and interviews to match you with the right person.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Recruitment Services",
      "category": "Domestic Services"
    },
    {
      "@type": "CollectionPage",
      "@id": "https://tcclab.paginasamarelas.co.mz//#services",
      "url": "https://tcclab.paginasamarelas.co.mz//#services",
      "name": "Our Services",
      "description": "Explore our range of domestic worker services including live-in nannies, live-out house helps, part-time babysitters, elderly care support, specialized domestic workers, and recruitment & placement services.",
      "isPartOf": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Live-In Nannies",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Live-Out House Helps",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Part-Time Babysitters",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Elderly Care Support",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          },
          {
            "@type": "ListItem",
            "position": 5,
            "name": "Specialized Domestic Workers",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          },
          {
            "@type": "ListItem",
            "position": 6,
            "name": "Recruitment & Placement",
            "url": "https://tcclab.paginasamarelas.co.mz//#services"
          }
        ]
      }
    },
    {
      "@type": "ContactPage",
      "@id": "https://tcclab.paginasamarelas.co.mz//#contact",
      "url": "https://tcclab.paginasamarelas.co.mz//#contact",
      "name": "Contact Us",
      "description": "Contact information for House Girls Village and Bureau including physical address, phone numbers, email, and working hours.",
      "isPartOf": {
        "@id": "https://tcclab.paginasamarelas.co.mz//#website"
      },
      "mainEntity": {
        "@type": "ContactPoint",
        "telephone": "+254726699446",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English",
        "areaServed": "KE",
        "hoursAvailable": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"
          ],
          "opens": "08:00",
          "closes": "17:00"
        }
      }
    },
    {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://tcclab.paginasamarelas.co.mz//"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "About Us",
          "item": "https://tcclab.paginasamarelas.co.mz//#about"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Services",
          "item": "https://tcclab.paginasamarelas.co.mz//#services"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "Contact",
          "item": "https://tcclab.paginasamarelas.co.mz//#contact"
        }
      ]
    }
  ]
}
</script>



</head>

<body>
 
     <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="70" height="70">
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">Sobre Nós</a>
                <a href="#services">Serviços</a>
                <a href="#contact">Contactos</a>

            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Moçambique" class="contact-btn">Contactos</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logo.webp" srcset="./img/logo.webp 1x, ./img/logo.webp 2x" alt="Logo" title="Logo" width="70" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                 <a href="#about">Sobre Nós</a>
                <a href="#services">Serviços</a>
                <a href="#contact">Contactos</a>
                <a href="<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Moçambique" class="contact-btn">Contactos</a>

              
            </div>
        </div>
    </div>



<section class="hero-slider">

  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/slide1.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/slide1.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/slide1.webp">
        <img
          src="./img/slide1.webp"
          alt="slider 8"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          >
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">TCC Lab</span>
        
        <p> A TCC Lab é uma iniciativa focada em apoiar estudantes universitários em
          Moçambique na elaboração de suas monografias..</p>
      <a  href="#services" class="explore-btn">Serviços</a>
      </div>
    </div>
  </div>

 
</section>


    <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/img12.webp"
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp" 
    srcset="./img/img12.webp"
  >
  <img
    src="./img/img12.webp"
    alt="Tcc Lab sobre nós image"
    loading="lazy"
    title="Tcc Lab"

  >
</picture>




  </div>


</div>

                <div class="about-text">
                    <span class="abt">Sobre Nós
</span>
                    
                  <p>No TCC Lab acreditamos no poder transformador do conhecimento e no potencial ilimitado que reside em cada jovem académico. A nossa história é de propósito, dedicação e compromisso resoluto em fomentar o brilho académico em Moçambique.</p>
                  <p>Fundado com uma visão ousada, o TCC Lab é uma iniciativa que representa um farol de esperança para estudantes universitários da região e de outros lugares. Temos a missão de revolucionar o processo de desenvolvimento de monografias, tornando-o mais eficiente e, o mais importante, garantindo o sucesso de nossos promissores académicos.
</p>
<p>Em nosso Laboratório também oferecemos mentoria personalizada orientando cada aluno em sua jornada académica. Nossos mentores não são apenas educadores, mas também companheiros, apoiando os alunos em cada etapa do caminho.
</p>




          
               
                </div>


               

            </div>
        </div>
    </div>

    <div class="about-us" id="about">
      <div class="content-grid">
          <div class="about-content">
             
              <div class="about-text">
                  
<p>O conhecimento não conhece fronteiras e nosso objetivo é remover quaisquer barreiras que possam impedir o sucesso de nossos alunos. O TCC Lab oferece acesso a uma grande variedade de recursos especializados, desde bibliotecas de trabalhos académicos até ferramentas de pesquisa de ponta. Capacitamos os alunos a explorar os campos escolhidos com confiança.
</p>
<p>Recebemos estudantes de todas as áreas, seja em humanidades, ciências, engenharia ou artes. Nossa comunidade se estende a estudantes de 18 a 30 anos, instituições académicas, educadores apaixonados e pesquisadores dedicados, todos comprometidos com a excelência no ensino. Junte-se a nós hoje e juntos vamos construir um cenário educacional mais brilhante.
</p>



        
             
              </div>

              <div class="about-image">
                <div class="image-container main-image">
                <picture>
                <source 
                  media="(max-width: 499px)" 
                  type="image/webp" 
                  srcset="./img/img11.webp"
                >
                <source 
                  media="(min-width: 500px)" 
                  type="image/webp" 
                  srcset="./img/img11.webp"
                >
                <img
                  src="./img/img11.webp"
                  alt="Tcc Lab sobre nós image"
                  loading="lazy"
                  title="Tcc Lab "
                
                >
                </picture>
                
                
                
                
                </div>
                
                
                </div>
             

          </div>
      </div>
  </div>

<div class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        
        <div class="cards-container">
        
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M21 12c0 1.2-4.03 6-9 6s-9-4.8-9-6c0-1.2 4.03-6 9-6s9 4.8 9 6Z"></path>
                  <path stroke="currentColor" stroke-width="2" d="M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                </svg>
  
              </div>
              <span class="h3">Visão</span>
            </div>
            <p>Ser a principal referência na mentoria e apoio na elaboração de monografias em Moçambique, proporcionando aos alunos as ferramentas necessárias para desenvolverem trabalhos académicos de elevada qualidade.

</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3.78552 9.5 12.7855 14l9-4.5-9-4.5-8.99998 4.5Zm0 0V17m3-6v6.2222c0 .3483 2 1.7778 5.99998 1.7778 4 0 6-1.3738 6-1.7778V11"/>

                </svg>
              </div>
              <span class="h3">Missão</span>
            </div>
            <p>Facilitar o percurso académico dos alunos, oferecendo orientação especializada e recursos de qualidade para a elaboração de monografias excecionais, contribuindo assim para o sucesso académico e profissional.</p>
            <div class="card-overlay"></div>
          </div>
  
          
          
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m7.171 12.906-2.153 6.411 2.672-.89 1.568 2.34 1.825-5.183m5.73-2.678 2.154 6.411-2.673-.89-1.568 2.34-1.825-5.183M9.165 4.3c.58.068 1.153-.17 1.515-.628a1.681 1.681 0 0 1 2.64 0 1.68 1.68 0 0 0 1.515.628 1.681 1.681 0 0 1 1.866 1.866c-.068.58.17 1.154.628 1.516a1.681 1.681 0 0 1 0 2.639 1.682 1.682 0 0 0-.628 1.515 1.681 1.681 0 0 1-1.866 1.866 1.681 1.681 0 0 0-1.516.628 1.681 1.681 0 0 1-2.639 0 1.681 1.681 0 0 0-1.515-.628 1.681 1.681 0 0 1-1.867-1.866 1.681 1.681 0 0 0-.627-1.515 1.681 1.681 0 0 1 0-2.64c.458-.361.696-.935.627-1.515A1.681 1.681 0 0 1 9.165 4.3ZM14 9a2 2 0 1 1-4 0 2 2 0 0 1 4 0Z"/>

                </svg>
              </div>
              <span class="h3">Nossos valores</span>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
                Excelência académica
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
                Compromisso com o sucesso do aluno
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>  Inovação educacional
 </span> 
              </li>
                
              <li>
              
  
                <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                   <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                 </svg>  Suporte abrangente e acessível
  </span> 
               </li>


               <li>
              
  
                <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                   <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                 </svg>  Conexão com a comunidade académica
  </span> 
               </li>


               <li>
              
  
                <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                   <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                 </svg>  Capacitação estudantil
  </span> 
               </li>

              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>



<div class="card" data-tilt="">
  <div class="card-header">
    <div class="card-icon">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.5 11.5 11 14l4-4m6 2a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>

      </svg>
    </div>
    <span class="h3">Nossos objetivos</span>
  </div>
  <ul class="custom-list">
    <li>
     

     <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
      </svg>

      Oferecer mentoria especializada em todas as etapas da monografia.
</span>
    </li>
    <li>
     
     <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
      </svg>

      Garantir a satisfação e o crescimento académico dos nossos pupilos.
</span>
    </li>
    <li>
    

     <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
      </svg>  Contribuir para a entrega de monografias de alta qualidade e em grande quantidade em todo o território nacional.
</span> 
    </li>
      
    <li>
    

      <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
         <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
       </svg>  Expandir a nossa influência na divulgação de recursos educativos.
 </span> 
     </li>


    <li>
    

     </li>

   
   
  </ul>
  <div class="card-overlay"></div>
</div>



        </div>
      </div>
    </div>
    </div>
    
  </div>


   <div class="fireprod-header" id="services">
        <div class="fireprod-container">
            <h2 class="fireprod-section-title">Nossos serviços
</h2>

            <div class="content-grid">
                <div class="fireprod-grid">
                    


                    <div class="fireprod-card">
                        <img src="./img/img4.webp" alt="Live-In Nannies" title="Live-In Nannies" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Mentoria personalizada</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Se precisar de ajuda com pesquisa, estruturação de sua monografia ou aprimoramento de suas habilidades de redação, nossos mentores estão aqui para apoiá-lo em cada etapa de sua jornada académica.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/img5.webp" alt="Live-Out House Helps" title="Live-Out House Helps"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Recursos especializados</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>Oferecemos acesso a uma variedade de recursos especializados, incluindo bibliotecas académicas, bancos de dados de pesquisa e materiais académicos.




                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/img6.webp" alt="Part-Time Babysitters" title="Part-Time Babysitters"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Ferramentas Inovadoras</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>Fique na vanguarda da tecnologia académica com nossas ferramentas inovadoras. Essas ferramentas simplificam tarefas complexas, ajudando a economizar tempo e a trabalhar com mais eficiência.


                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    
                    <div class="fireprod-card">
                        <img src="./img/img8.webp" alt="Elderly Care Support" title="Elderly Care Support" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Cobertura diversificada de campo </p>
                            <div class="product-services-container">
                                <ul>
                                    <li>O TCC Lab atende alunos de todas as áreas do conhecimento, garantindo que, quer esteja estudando humanidades, ciências, engenharia ou artes, encontrará o suporte e os recursos necessários.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                      <div class="fireprod-card">
                        <img src="./img/img7.webp" alt="Specialized Domestic Workers" title="Specialized Domestic Workers" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Envolvimento da Comunidade</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Nossos serviços não se limitam apenas aos estudantes. Através da colaboração, nos envolvemos com instituições académicas e professores para criar uma comunidade vibrante.


                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>
                      

                  

                </div>
            </div>
        </div>
    </div>


  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contactos
</h2>
      <div class="contact-content">
        <div class="contact-map">


          <iframe src="https://www.google.com/maps/embed?pb=!1m14!1m8!1m3!1d8827.605912669305!2d32.595679!3d-25.965067!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x1ee69bb1b3755f3f%3A0xbc5b08c6683fe739!2s324%20Avenida%20Kwame%20Nkrumah%2C%20Maputo%2C%20Mo%C3%A7ambique!5e1!3m2!1spt-PT!2scv!4v1759421393369!5m2!1spt-PT!2scv" width="600" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade" title="TCC LAB"></iframe>

        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Localização</h3>
              <p>Av. Kwame Nkrumah 324 Maputo, 1163</p>


            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Telefone</h3>
              <p><a href="tel:+258 842931222 ">+258 842931222 
</a></p>

    <p><a href="tel:82 2929299">82 2929299
</a></p>

            
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="<EMAIL>?subject=Quote+Requested+through+Yellow+Pages+Moçambique"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Horário comercial</h3>
              <p>Segunda a sábado: 8h00 – 17h00</p>


            </div>
          </div>
          <!-- Social Media Icons -->

          
          
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy;<span id="current-year"></span> TCC Lab. Todos os direitos reservados.</p>

        </div>
        <div class="designer">
          <a href="https://paginasamarelas.co.mz/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Yellow Pages Kenya" width="50" height="50"
              title="Yellow Pages Kenya">
            <p>Desenvolvido por Páginas Amarelas Moçambique</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <script src="./js/testimonial.js"></script>
  <!-- <script src="./js/main.js"></script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->



  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
<script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

 


</body>

</html>