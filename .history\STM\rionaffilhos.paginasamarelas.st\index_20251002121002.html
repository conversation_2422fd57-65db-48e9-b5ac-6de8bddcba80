<!DOCTYPE html>
<html lang="pt">

<head>
  <meta charset="utf-8">
  <title>Rionaf & <PERSON>hos, Lda</title>
  <meta name="description" content="Há mais de 32 anos, a Rionaf & Filhos é líder de confiança em serviços de importação, exportação e marítimos em São Tomé e Príncipe. Desde o desembaraço aduaneiro e gestão de navios até viagens e agricultura, garantimos uma logística perfeita e parcerias fiáveis. Entre em contacto connosco hoje mesmo!">
  <meta name="keywords" content="House Girls Village and Bureau, Nairobi, house help in Nairobi, Nanny in Nairobi ">
  <meta content="width=device-width, initial-scale=1.0" name="viewport">
  <!-- Favicon -->
  <link rel="icon" href="./favicon/favicon.ico">
  <link rel="apple-touch-icon" sizes="180x180" href="./favicon/apple-touch-icon.png">
  <link rel="icon" type="image/jpg" sizes="32x32" href="./favicon/favicon-32x32.png">
  <link rel="icon" type="image/jpg" sizes="16x16" href="./favicon/favicon-16x16.png">
  <link rel="manifest" href="./favicon/site.webmanifest.json">
  <!-- Robots -->
  <meta name="robots" content="index, follow">
  <!-- Site Published Date -->
  <meta property="article:published_time" content="2025-09-25">
  <!-- Google Verification -->
  <!-- <meta name="google-site-verification" content="Your Google Search Console Verification Code"> -->
   <meta name="google-site-verification" content="N0HompzJ25uz2PejDBXT-26yePXEWujgsnsWOcVrNQQ" />
  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:url" content="https://rionaffilhos.paginasamarelas.st">
  <meta property="og:title" content="Rionaf & Filhos, Lda">
  <meta property="og:description" content="Há mais de 32 anos, a Rionaf & Filhos é líder de confiança em serviços de importação, exportação e marítimos em São Tomé e Príncipe. Desde o desembaraço aduaneiro e gestão de navios até viagens e agricultura, garantimos uma logística perfeita e parcerias fiáveis. Entre em contacto connosco hoje mesmo!">
  <meta property="og:image" content="https://rionaffilhos.paginasamarelas.st/img/logorionafweb.webp">
  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:site" content="@yellowpages254">
  <meta property="twitter:url" content="https://rionaffilhos.paginasamarelas.st">
  <meta property="twitter:title" content="Rionaf & Filhos, Lda ">
  <meta property="twitter:description" content="Há mais de 32 anos, a Rionaf & Filhos é líder de confiança em serviços de importação, exportação e marítimos em São Tomé e Príncipe. Desde o desembaraço aduaneiro e gestão de navios até viagens e agricultura, garantimos uma logística perfeita e parcerias fiáveis. Entre em contacto connosco hoje mesmo!">
  <meta property="twitter:image" content="https://rionaffilhos.paginasamarelas.st/img/logorionafweb.webp">
  <!-- Canonical URL -->
  <link rel="canonical" href="https://rionaffilhos.paginasamarelas.st">
  <!-- Hreflang tags -->
  <link rel="alternate" hreflang="en" href="https://rionaffilhos.paginasamarelas.st">
  <!-- Include more hreflang tags here if you have the website available in other languages -->
  <!-- Sitemap -->
  <link rel="sitemap" type="application/xml" title="Sitemap"
    href="https://rionaffilhos.paginasamarelas.st/sitemap.xml">

  <!-- Preconnect to Google Maps APIs -->
  <link rel="preconnect" href="https://maps.googleapis.com" crossorigin>
  <link rel="preconnect" href="https://maps.gstatic.com" crossorigin>
  <link rel="preload" as="image" href="./img/banner.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/banner.webp" fetchpriority="high">
  <link rel="preload" as="image" href="./img/banner.webp" fetchpriority="high">
  <!-- Internal CSS -->

  <link rel="stylesheet" href="css/features.css">
  <link rel="stylesheet" href="css/ots.css">
  <link rel="stylesheet" href="css/s2.css">
  <link rel="stylesheet" href="css/hero.css">
  <link rel="stylesheet" href="css/service-section.css">
  <link rel="stylesheet" href="css/mn.css">
  <link rel="stylesheet" href="css/about.css">
  <link rel="stylesheet" href="css/main.css">
  <link rel="stylesheet" href="css/services.css">
  <link rel="stylesheet" href="css/testimonial.css">
  <!-- <link rel="stylesheet" href="css/categories.css"> -->
  <!-- 
  <link rel="alternate" hreflang="en" href="https://rionaffilhos.paginasamarelas.st/"> -->
  <link rel="alternate" hreflang="x-default" href="https://rionaffilhos.paginasamarelas.st/">

 <!-- Google tag (gtag.js) -->
<script async src="https://www.googletagmanager.com/gtag/js?id="></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', '');
</script>

  <style>
    html {
      scroll-behavior: smooth;
    }

    /* Inline critical font styles for Poppins */
    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 400;
      src: local('Poppins Regular'), local('Poppins-Regular'), url(https://fonts.gstatic.com/s/poppins/v15/pxiEyp8kv8JHgFVrJJfecg.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Poppins';
      font-style: normal;
      font-weight: 700;
      src: local('Poppins Bold'), local('Poppins-Bold'), url(https://fonts.gstatic.com/s/poppins/v15/pxiByp8kv8JHgFVrLCz7Z1xlFd2JQEk.woff2) format('woff2');
      font-display: swap;
    }

    /* Inline critical font styles for Work Sans */

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 400;
      src: local('Work Sans Regular'),
        local('WorkSans-Regular'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 600;
      src: local('Work Sans SemiBold'),
        local('WorkSans-SemiBold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }

    @font-face {
      font-family: 'Work Sans';
      font-style: normal;
      font-weight: 700;
      src: local('Work Sans Bold'),
        local('WorkSans-Bold'),
        url(https://fonts.gstatic.com/s/worksans/v19/QGYsz_wNahGAdqQ43Rh_fKDp.woff2) format('woff2');
      font-display: swap;
    }


    body {
      font-family: 'Work Sans', sans-serif;
    }

    .mobile-menu {
      transition: transform 0.3s ease-in-out;
    }

    .mobile-menu.hidden {
      transform: translateX(-100%);
    }

    #top-bar {
      transition: transform 0.3s ease-out, opacity 0.3s ease-out;
    }

    #main-nav {
      transition: all 0.3s ease-out;
    }

    .sticky {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      z-index: 50;
      background-color: white;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  </style>

<script type="application/ld+json">
{
  "@context": "https://schema.org",
  "@graph": [
    {
      "@type": "Organization",
      "@id": "https://rionaffilhos.paginasamarelas.st/#organization",
      "name": "House Girls Village and Bureau",
      "url": "https://rionaffilhos.paginasamarelas.st",
      "logo": "https://rionaffilhos.paginasamarelas.st/img/logorionafweb.webp",
      "description": "Há mais de 32 anos, a Rionaf & Filhos é líder de confiança em serviços de importação, exportação e marítimos em São Tomé e Príncipe. Desde o desembaraço aduaneiro e gestão de navios até viagens e agricultura, garantimos uma logística perfeita e parcerias fiáveis. Entre em contacto connosco hoje mesmo!",
      "contactPoint": {
        "@type": "ContactosPoint",
        "telephone": "+254726699446",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English"
      },
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Westlands Rd",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "sameAs": [
        "https://www.facebook.com/p/House-Girls-Village-and-Bureau-Nairobi-100089786383876/",
        "https://api.whatsapp.com/send/?phone=+239 2223575"
      ]
    },
    {
      "@type": "LocalBusiness",
      "@id": "https://rionaffilhos.paginasamarelas.st/#localbusiness",
      "name": "House Girls Village and Bureau",
      "image": "https://rionaffilhos.paginasamarelas.st/img/logorionafweb.webp",
      "url": "https://rionaffilhos.paginasamarelas.st",
      "telephone": "+254726699446",
      "email": "<EMAIL>",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "Westlands Rd",
        "addressLocality": "Nairobi",
        "addressCountry": "Kenya"
      },
      "openingHours": "Mo-Sa 08:00-17:00",
      "priceRange": "$$",
      "geo": {
        "@type": "GeoCoordinates",
        "latitude": "-1.2695058000000001",
        "longitude": "36.8087903"
      }
    },
    {
      "@type": "WebSite",
      "@id": "https://rionaffilhos.paginasamarelas.st/#website",
      "url": "https://rionaffilhos.paginasamarelas.st",
      "name": "House Girls Village and Bureau",
      "description": "Reliable Nannies and House Helps for Your Home",
      "publisher": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#organization"
      },
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://rionaffilhos.paginasamarelas.st/?s={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    },
    {
      "@type": "WebPage",
      "@id": "https://rionaffilhos.paginasamarelas.st/#webpage",
      "url": "https://rionaffilhos.paginasamarelas.st",
      "name": "Rionaf & Filhos, Lda",
      "isPartOf": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#website"
      },
      "about": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#organization"
      },
      "datePublished": "2025-09-25",
      "description": "Há mais de 32 anos, a Rionaf & Filhos é líder de confiança em serviços de importação, exportação e marítimos em São Tomé e Príncipe. Desde o desembaraço aduaneiro e gestão de navios até viagens e agricultura, garantimos uma logística perfeita e parcerias fiáveis. Entre em contacto connosco hoje mesmo!",
      "inLanguage": "en",
      "potentialAction": {
        "@type": "ReadAction",
        "target": [
          "https://rionaffilhos.paginasamarelas.st"
        ]
      }
    },
    {
      "@type": "AboutPage",
      "@id": "https://rionaffilhos.paginasamarelas.st/#about",
      "url": "https://rionaffilhos.paginasamarelas.st/#about",
      "name": "Sobre nós",
      "description": "Learn about House Girls Village and Bureau, Nairobi - your trusted partner for finding reliable domestic workers.",
      "isPartOf": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#website"
      },
      "mainEntity": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau",
        "description": "A domestic worker agency in Nairobi that connects families with trusted, trained, and dependable domestic workers.",
        "foundingDate": "2025-09-25",
        "serviceArea": {
          "@type": "Country",
          "name": "Kenya"
        },
        "hasOfferCatalog": {
          "@type": "OfferCatalog",
          "name": "Domestic Worker Serviços",
          "itemListElement": [
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Live-In Nannies"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Desembaraço aduaneiro"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Agência marítima e gestão comercial"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Serviços de viagens"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Café e agricultura"
              }
            },
            {
              "@type": "Offer",
              "itemOffered": {
                "@type": "Service",
                "name": "Hotelaria (serviços de pensão)"
              }
            }
          ]
        },
        "knowsAbout": [
          "Domestic Worker Placement",
          "Nanny Serviços",
          "House Help Serviços",
          "Elderly Care",
          "Childcare Serviços",
          "Household Staff Recruitment"
        ],
        "values": [
          {
            "@type": "DefinedTerm",
            "name": "Fiabilidade",
            "description": "We maintain high standards of professionalism in all our services and interactions."
          },
          {
            "@type": "DefinedTerm",
            "name": "Care",
            "description": "We show genuine care for both families and domestic workers in our placement process."
          },
          {
            "@type": "DefinedTerm",
            "name": "Reliability",
            "description": "We provide reliable and trustworthy domestic workers to ensure peace of mind for families."
          }
        ]
      }
    },
    {
      "@type": "Service",
      "name": "Live-In Nannies",
      "description": "Full-time caregivers who stay in your home to provide round-the-clock support for your children and household.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Childcare Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "Service",
      "name": "Desembaraço aduaneiro",
      "description": "Reliable domestic workers who come in daily to assist with cleaning, cooking, laundry, and other household chores.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Housekeeping Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "Service",
      "name": "Agência marítima e gestão comercial",
      "description": "Flexible babysitting services for parents who need short-term or occasional child care.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Childcare Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "Service",
      "name": "Serviços de viagens",
      "description": "Compassionate helpers trained to assist seniors with daily routines, companionship, and light household duties.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Elderly Care Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "Service",
      "name": "Café e agricultura",
      "description": "Trained staff for specific needs such as cooking, cleaning, or childcare, depending on your family's requirements.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Specialized Domestic Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "Service",
      "name": "Hotelaria (serviços de pensão)",
      "description": "A stress-free hiring process with thorough background checks, reference verification, and interviews to match you with the right person.",
      "provider": {
        "@type": "Organization",
        "name": "House Girls Village and Bureau"
      },
      "serviceType": "Recruitment Serviços",
      "category": "Domestic Serviços"
    },
    {
      "@type": "CollectionPage",
      "@id": "https://rionaffilhos.paginasamarelas.st/#services",
      "url": "https://rionaffilhos.paginasamarelas.st/#services",
      "name": "Our Serviços",
      "description": "Explore our range of domestic worker services including live-in nannies, live-out house helps, part-time babysitters, elderly care support, specialized domestic workers, and recruitment & placement services.",
      "isPartOf": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#website"
      },
      "mainEntity": {
        "@type": "ItemList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Live-In Nannies",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "Desembaraço aduaneiro",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": "Agência marítima e gestão comercial",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": "Serviços de viagens",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          },
          {
            "@type": "ListItem",
            "position": 5,
            "name": "Café e agricultura",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          },
          {
            "@type": "ListItem",
            "position": 6,
            "name": "Hotelaria (serviços de pensão)",
            "url": "https://rionaffilhos.paginasamarelas.st/#services"
          }
        ]
      }
    },
    {
      "@type": "ContactosPage",
      "@id": "https://rionaffilhos.paginasamarelas.st/#contact",
      "url": "https://rionaffilhos.paginasamarelas.st/#contact",
      "name": "Contacte-nos",
      "description": "Contactos information for House Girls Village and Bureau including physical address, phone numbers, email, and working hours.",
      "isPartOf": {
        "@id": "https://rionaffilhos.paginasamarelas.st/#website"
      },
      "mainEntity": {
        "@type": "ContactosPoint",
        "telephone": "+254726699446",
        "contactType": "Customer Service",
        "email": "<EMAIL>",
        "availableLanguage": "English",
        "areaServed": "KE",
        "hoursAvailable": {
          "@type": "OpeningHoursSpecification",
          "dayOfWeek": [
            "Monday",
            "Tuesday",
            "Wednesday",
            "Thursday",
            "Friday",
            "Saturday"
          ],
          "opens": "08:00",
          "closes": "17:00"
        }
      }
    },
    {
      "@type": "BreadcrumbList",
      "itemListElement": [
        {
          "@type": "ListItem",
          "position": 1,
          "name": "Home",
          "item": "https://rionaffilhos.paginasamarelas.st/"
        },
        {
          "@type": "ListItem",
          "position": 2,
          "name": "Sobre nós",
          "item": "https://rionaffilhos.paginasamarelas.st/#about"
        },
        {
          "@type": "ListItem",
          "position": 3,
          "name": "Serviços",
          "item": "https://rionaffilhos.paginasamarelas.st/#services"
        },
        {
          "@type": "ListItem",
          "position": 4,
          "name": "Contactos",
          "item": "https://rionaffilhos.paginasamarelas.st/#contact"
        }
      ]
    }
  ]
}
</script>

</head>

<body>
 
     <!-- Main Navigation -->
    <nav id="main-nav" class="content-grid">
        <div class="nav-inner">
            <div class="logo">
                <a href="/">
                  <img src="./img/logorionafweb.webp" srcset="./img/logorionafweb.webp 1x, ./img/logorionafweb.webp 2x" alt="Logo" title="Logo" width="100" height="100">
                </a>
            </div>
            <div class="desktop-menu">
                <a href="/">Home</a>
                <a href="#about">Sobre nós</a>
                <a href="#services">Serviços</a>
                <a href="#contact">Contactos</a>

            </div>
            <button id="mobile-menu-toggle" class="mobile-menu-toggle">&#9776;</button>
            <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Paginas+Amarelas+Sao+Tome" class="contact-btn">Contacte-nos</a>
        </div>
    </nav>

    <!-- Mobile Menu Overlay -->
    <div id="mobile-menu" class="mobile-menu">
        <div class="mobile-menu-content">
            <div class="menu-header">
                <img src="./img/logorionafweb.webp" srcset="./img/logorionafweb.webp 1x, ./img/logorionafweb.webp 2x" alt="Logo" title="Logo" width="70" height="70">
                <button id="close-mobile-menu">&times;</button>
            </div>
            <div class="menu-links">
                <a href="/">Home</a>
                 <a href="#about">Sobre nós</a>
                <a href="#services">Serviços</a>
                <a href="#contact">Contactos</a>
                <a href="mailto:<EMAIL>?subject=Quote+Requested+through+Paginas+Amarelas+Sao+Tome" class="contact-btn">Contacte-nos</a>

              
            </div>
        </div>
    </div>



<section class="hero-slider">

  <!-- Slide 1 -->
  <div class="slide active">
    <div class="slide-bg">
      <picture>
        <source 
          media="(max-width: 799px)" 
          type="image/webp" 
          srcset="./img/banner.webp">
        <source 
          media="(min-width: 800px) and (max-width: 1214px)" 
          type="image/webp" 
          srcset="./img/banner.webp">
        <source 
          media="(min-width: 1215px)" 
          type="image/webp" 
          srcset="./img/banner.webp">
        <img
          src="./img/banner.webp"
          alt="slider 8"
          class="hero-img"
          width="1400"
          height="600"
          decoding="async"
          >
      </picture>
    </div>
    <div class="slide-overlay"></div>
    <div class="text-overlay"></div>
    <div class="content-grid">
      <div class="hero-content">
        <span class="h6">Rionaf & Filhos, Lda</span>
        <h1>Agência Africa Marline</h1>
      <a  href="#services" class="explore-btn">Serviços</a>
      </div>
    </div>
  </div>

 
</section>


    <div class="about-us" id="about">
        <div class="content-grid">
            <div class="about-content">
               <div class="about-image">
  <div class="image-container main-image">
<picture>
  <source 
    media="(max-width: 499px)" 
    type="image/webp" 
    srcset="./img/sobre_nos.webp"
  >
  <source 
    media="(min-width: 500px)" 
    type="image/webp" 
    srcset="./img/sobre_nos.webp"
  >
  <img
    src="./img/sobre_nos.webp"
    alt="Rionaf about image"
    loading="lazy"
    height="447px"
    title="Rionaf"

  >
</picture>




  </div>


</div>

                <div class="about-text">
                    <span class="abt">Sobre nós
</span>
                    
                  <p>Na Rionaf & Filhos temos mais de 32 anos de experiência no setor, servindo São Tomé e Príncipe desde 2014. Somos reconhecidos nacional e internacionalmente pela nossa experiência em serviços de importação e exportação, desembaraço aduaneiro e soluções de agenciamento marítimo.</p>
                  <p>As nossas operações abrangem diversos setores, incluindo agenciamento marítimo e gestão comercial, viagens, café, agricultura e hotelaria. Desde a gestão de navios de carga e passageiros até ao apoio ao comércio e prestação de serviços de qualidade, continuamos comprometidos com a fiabilidade, excelência e parcerias de longo prazo. Entre em contacto connosco hoje mesmo!
</p>

<div class="about-services-container">
                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                Serviços de importação e exportação

                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                    Desembaraço aduaneiro

                            </li>

                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                        Agência marítima e gestão comercial
                            </li>

                          
                            

                        </ul>

                        <ul>
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
                         Serviços de viagens
                            </li>
                            
                            <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
          Café e agricultura
                            </li>

                              <li>
                                <span>
                                    <svg class="tick-icon" xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                                    </svg>
                                </span>
          Hotelaria (serviços de pensão)
                            </li>

                          
                            
                        </ul>
                    </div>
          
               
                </div>
            </div>
        </div>
    </div>

<div class="features">
    <div class="pattern-overlay"></div>
    <div class="feature-container">
    <div class="content-grid">
      <div class="grid-container">
        
        <div class="cards-container">
        
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
            
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
              </div>
              <span class="h3">Missão</span>
            </div>
            <p>Prestar serviços fiáveis e inovadores de importação, exportação e marítimos, promovendo simultaneamente o comércio, a agricultura e o turismo em São Tomé e Príncipe com integridade, excelência e um compromisso com o crescimento sustentável.

</p>
            <div class="card-overlay"></div>
          </div>
  
  
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5m-9-6h.008v.008H12v-.008ZM12 15h.008v.008H12V15Zm0 2.25h.008v.008H12v-.008ZM9.75 15h.008v.008H9.75V15Zm0 2.25h.008v.008H9.75v-.008ZM7.5 15h.008v.008H7.5V15Zm0 2.25h.008v.008H7.5v-.008Zm6.75-4.5h.008v.008h-.008v-.008Zm0 2.25h.008v.008h-.008V15Zm0 2.25h.008v.008h-.008v-.008Zm2.25-4.5h.008v.008H16.5v-.008Zm0 2.25h.008v.008H16.5V15Z"></path>
                </svg>
              </div>
              <span class="h3">Visão</span>
            </div>
            <p>Ser um parceiro de confiança no comércio global, viagens e serviços marítimos.</p>
            <div class="card-overlay"></div>
          </div>
  
          <div class="card" data-tilt="">
            <div class="card-header">
              <div class="card-icon">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke="currentColor">
                  <path d="M6.429 9.75 2.25 12l4.179 2.25m0-4.5 5.571 3 5.571-3m-11.142 0L2.25 7.5 12 2.25l9.75 5.25-4.179 2.25m0 0L21.75 12l-4.179 2.25m0 0 4.179 2.25L12 21.75 2.25 16.5l4.179-2.25m11.142 0-5.571 3-5.571-3"></path>
                </svg>
              </div>
              <span class="h3">Valores Fundamentais</span>
            </div>
            <ul class="custom-list">
              <li>
               
  
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
              Fiabilidade
 </span>
              </li>
              <li>
               
               <span> <svg xmlns="http://www.w3.org/2000/svg" fill="none" width="24" height="24" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>
  
           Integridade 
</span>
              </li>
              <li>
              
  
               <span><svg xmlns="http://www.w3.org/2000/svg" fill="none" width="34" height="34" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9 12.75 11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 0 1-1.043 3.296 3.745 3.745 0 0 1-3.296 1.043A3.745 3.745 0 0 1 12 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 0 1-3.296-1.043 3.745 3.745 0 0 1-1.043-3.296A3.745 3.745 0 0 1 3 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 0 1 1.043-3.296 3.746 3.746 0 0 1 3.296-1.043A3.746 3.746 0 0 1 12 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 0 1 3.296 1.043 3.746 3.746 0 0 1 1.043 3.296A3.745 3.745 0 0 1 21 12Z"></path>
                </svg>  Parceria
 </span> 
              </li>
                
                


              <li>
              

               </li>
  
             
             
            </ul>
            <div class="card-overlay"></div>
          </div>
          
        </div>
      </div>
    </div>
    </div>
    
  </div>


   <div class="fireprod-header" id="services">
        <div class="fireprod-container">
            <h2 class="fireprod-section-title">Nossos Serviços
</h2>

            <div class="content-grid">
                <div class="fireprod-grid">
                    


                    <div class="fireprod-card">
                        <img src="./img/import_e_exporrt.webp" alt="Serviços de importação e exportação" title="Serviços de importação e exportação" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Serviços de importação e exportação</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Facilitamos a circulação fluida de mercadorias através das fronteiras, garantindo uma logística eficiente, conformidade com os regulamentos internacionais e entregas fiáveis.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/fiscal.webp" alt="Desembaraço aduaneiro" title="Desembaraço aduaneiro"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Desembaraço aduaneiro</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>A nossa equipa trata dos procedimentos aduaneiros para navios de carga e de passageiros, tornando o comércio e as viagens fáceis e sem complicações.




                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    <div class="fireprod-card">
                        <img src="./img/container.webp" alt="Agência marítima e gestão comercial" title="Agência marítima e gestão comercial"  loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Agência marítima e gestão comercial</p>

                             <div class="product-services-container">
                                <ul>
                                    <li>Prestamos serviços completos de agência marítima, incluindo apoio às operações portuárias, assistência à tripulação, manuseamento de carga e gestão comercial de navios.
                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                    
                    <div class="fireprod-card">
                        <img src="./img/viagem.webp" alt="Serviços de viagens" title="Serviços de viagens" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Serviços de viagens </p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Como agência de viagens licenciada, ajudamos no planeamento, reservas e logística de viagens, garantindo viagens confortáveis e sem stress.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                      <div class="fireprod-card">
                        <img src="./img/cafe.webp" alt="Café e agricultura" title="Café e agricultura" height="326px" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Café e agricultura</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Atuamos no comércio de café e serviços agrícolas, apoiando os agricultores locais e promovendo o rico potencial agrícola de São Tomé e Príncipe.


                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>
                      <div class="fireprod-card">
                        <img src="./img/airbnb.webp" alt="Hotelaria (serviços de pensão)" title="Hotelaria (serviços de pensão)" loading="lazy">
                        <div class="fireprod-content">
                            <p class="fireprod-text">Hotelaria (serviços de pensão)</p>
                            <div class="product-services-container">
                                <ul>
                                    <li>Através dos nossos serviços de pensão, oferecemos acomodações confortáveis e atendimento personalizado, combinando hospitalidade com conveniência para os viajantes.

                                   </li>
                                   
                                </ul>
                                
                            </div>
                        </div>
                    </div>

                  

                </div>
            </div>
        </div>
    </div>


  <section class="contact" id="contact">
    <div class="content-grid">
      <h2 class="section-title">Contacte-nos</h2>
      <div class="contact-content">
        <div class="contact-map">


          <iframe title="map" src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3989.749523265712!2d6.722516675819875!3d0.3364702639931091!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x10766b0848e818ff%3A0xc39db91204736500!2sRionaf%20%26%20Filhos!5e0!3m2!1spt-PT!2scv!4v1759251895266!5m2!1spt-PT!2scv" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>


        </div>
        <div class="contact-info">
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0C158.75 0 80 78.75 80 176C80 267.4 256 512 256 512C256 512 432 267.4 432 176C432 78.75 353.25 0 256 0zM256 240C220.65 240 192 211.35 192 176C192 140.65 220.65 112 256 112C291.35 112 320 140.65 320 176C320 211.35 291.35 240 256 240z">
                </path>
              </svg>
            </span>
            <div>
              <h3>Localização</h3>
              <p>Riboque Cidade Capital, São Tomé 853</p>
            </div>
          </div>
          <div class="contact-item">
            <span>
              <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="m497.39 361.8l-112-48a24 24 0 0 0-28 6.9l-49.6 60.6A370.66 370.66 0 0 1 130.6 204.11l60.6-49.6a23.94 23.94 0 0 0 6.9-28l-48-112A24.16 24.16 0 0 0 122.6.61l-104 24A24 24 0 0 0 0 48c0 256.5 207.9 464 464 464a24 24 0 0 0 23.4-18.6l24-104a24.29 24.29 0 0 0-14.01-27.6">
                </path>
              </svg>
            </span>
            <div>
              <h3>Telefone</h3>
              <p><a href="tel:+2392223575">+239 2223575</a></p>
            </div>
          </div>
          <div class="contact-item">
            <span> <svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M502.3 190.8c3.9-3.1 9.7-.2 9.7 4.7V400c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V195.6c0-5 5.7-7.8 9.7-4.7c22.4 17.4 52.1 39.5 154.1 113.6c21.1 15.4 56.7 47.8 92.2 47.6c35.7.3 72-32.8 92.3-47.6c102-74.1 131.6-96.3 154-113.7M256 320c23.2.4 56.6-29.2 73.4-41.4c132.7-96.3 142.8-104.7 173.4-128.7c5.8-4.5 9.2-11.5 9.2-18.9v-19c0-26.5-21.5-48-48-48H48C21.5 64 0 85.5 0 112v19c0 7.4 3.4 14.3 9.2 18.9c30.6 23.9 40.7 32.4 173.4 128.7c16.8 12.2 50.2 41.8 73.4 41.4">
                </path>
              </svg></span>
            <div>
              <h3>Email</h3>
              <p><a
                  href="mailto:<EMAIL>?subject=Quote+Requested+through+Paginas+Amarelas+Sao+Tome+e+Principe"><EMAIL></a>
              </p>
            </div>
          </div>
          <div class="contact-item">
            <span><svg xmlns="http://www.w3.org/2000/svg" width="1.5rem" height="1.5rem" viewBox="0 0 512 512"
                class="contact-icon">
                <path fill="currentColor"
                  d="M256 0a256 256 0 1 1 0 512a256 256 0 1 1 0-512m-24 120v136c0 8 4 15.5 10.7 20l96 64c11 7.4 25.9 4.4 33.3-6.7s4.4-25.9-6.7-33.3L280 243.2V120c0-13.3-10.7-24-24-24s-24 10.7-24 24">
                </path>
              </svg></span>
            <div>
              <h3>Horário de funcionamento</h3>
              <p>Segunda a sexta-feira: das 8h00 às 17h00</p>
            </div>
          </div>
          <!-- Social Media Icons -->
        </div>
      </div>
    </div>
  </section>

  <footer class="footer">
    <div class="content-grid">
      <div class="footer-content">
        <div class="copyright">
          <p>&copy;<span id="current-year"></span> Rionaf & Filhos, Lda. All Rights Reserved.</p>
        </div>
        <div class="designer">
          <a href="https://www.paginasamarelas.st/" target="_blank" rel="noopener noreferrer">
            <img src="./img/yp_logo.webp" loading="lazy" alt="Páginas Amarelas São Tomé & Príncipe" width="50" height="50"
              title="Páginas Amarelas São Tomé & Príncipe">
            <p>Powered by Páginas Amarelas</p>
          </a>
        </div>
      </div>
    </div>
  </footer>

  <!-- <script src="./js/testimonial.js"></script> -->
  <!-- <script src="./js/main.js"></script> -->

  <!-- <script src="https://cdn.jsdelivr.net/npm/vanilla-tilt@latest/dist/vanilla-tilt.min.js"></script> -->

  <script>
    document.getElementById('current-year').textContent = new Date().getFullYear();
  </script>

  <!-- <script>
    // Initialize VanillaTilt for 3D card effect
    VanillaTilt.init(document.querySelectorAll(".card"), {
      max: 5,
      speed: 400,
      glare: true,
      "max-glare": 0.2,
    });

    // Intersection Observer for scroll animations
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.style.opacity = 1;
          entry.target.style.transform = 'translateY(0)';
        }
      });
    }, {
      threshold: 0.1
    });

    // Observe all cards
    document.querySelectorAll('.card').forEach(card => {
      card.style.opacity = 0;
      card.style.transform = 'translateY(20px)';
      observer.observe(card);
    });
  </script> -->

  <script>
    document.addEventListener('DOMContentLoaded', () => {
      const topBar = document.getElementById('top-bar');
      const mainNav = document.getElementById('main-nav');
      const mainContent = document.querySelector('body'); // Adjust this selector if needed
      const mobileMenu = document.getElementById('mobile-menu');
      const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
      const closeMobileMenu = document.getElementById('close-mobile-menu');
      const mobileMenuLinks = document.querySelectorAll('.mobile-menu .menu-links a');

      // Mobile Menu Logic
      mobileMenuToggle.addEventListener('click', () => {
        mobileMenu.classList.add('show');
      });

      closeMobileMenu.addEventListener('click', () => {
        mobileMenu.classList.remove('show');
      });

      // Add click event listeners to all mobile menu links
      mobileMenuLinks.forEach(link => {
        link.addEventListener('click', function (event) {
          mobileMenu.classList.remove('show');

          const href = this.getAttribute('href');
          if (href.startsWith('#') && href !== '#') {
            event.preventDefault();

            const targetElement = document.querySelector(href);

            if (targetElement) {
              setTimeout(() => {
                const yOffset = -80;
                const y = targetElement.getBoundingClientRect().top + window.pageYOffset + yOffset;

                window.scrollTo({
                  top: y,
                  behavior: 'smooth'
                });
              }, 300);
            }
          }
        });
      });

      // Debug function to log sticky state
      function logStickyState() {
        //console.log('Scroll position:', window.scrollY);
        //console.log('mainNav has sticky class:', mainNav.classList.contains('sticky'));
        //console.log('mainNav style:', mainNav.style.cssText);
        //console.log('computed position:', window.getComputedStyle(mainNav).position);
      }

      // Improved Sticky Header Logic
      function handleScroll() {
        const scrollTop = window.scrollY || document.documentElement.scrollTop;
        //console.log('Scrolling, position:', scrollTop);

        if (scrollTop > 50) {
          // Make sure we're applying direct styles
          mainNav.style.position = 'fixed';
          mainNav.style.top = '0';
          mainNav.style.left = '0';
          mainNav.style.width = '100%';
          mainNav.style.zIndex = '100';
          mainNav.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
          mainNav.classList.add('sticky');

          // Add padding to body to prevent content jump
          mainContent.style.paddingTop = mainNav.offsetHeight + 'px';

          // Hide the top bar
          if (topBar) {
            topBar.style.display = 'none';
          }

          logStickyState();
        } else {
          // Remove direct styles
          mainNav.style.position = '';
          mainNav.style.top = '';
          mainNav.style.left = '';
          mainNav.style.width = '';
          mainNav.style.zIndex = '';
          mainNav.style.boxShadow = '';
          mainNav.classList.remove('sticky');

          // Remove padding from body
          mainContent.style.paddingTop = '0';

          // Show the top bar on desktop
          if (topBar && window.innerWidth >= 1024) {
            topBar.style.display = 'block';
          }

          logStickyState();
        }
      }

      // Initial check on page load
      handleScroll();

      // Add scroll event listener
      window.addEventListener('scroll', handleScroll);

      // Handle window resize
      window.addEventListener('resize', () => {
        if (window.innerWidth < 1024 && topBar) {
          topBar.style.display = 'none';
        } else if (window.scrollY <= 50 && topBar) {
          topBar.style.display = 'block';
        }

        // Recalculate sticky state on resize
        handleScroll();
      });
    });
  </script>



  <script>
   
    function throttle(fn, limit) {
      let waiting = false;
      return function (...args) {
        if (!waiting) {
          fn.apply(this, args);
          waiting = true;
          setTimeout(() => waiting = false, limit);
        }
      };
    }

    function handleParallaxScroll() {
      const elements = document.querySelectorAll('[data-parallax]');
      const scrollY = window.scrollY;

      elements.forEach(el => {
        const container = el.closest('.parallax-container');
        const rect = container.getBoundingClientRect();
        const offsetTop = container.offsetTop;
        const height = container.offsetHeight;

        // Only calculate if it's in view
        if (scrollY + window.innerHeight > offsetTop && scrollY < offsetTop + height) {
          const speed = 0.5; // Adjust this to control intensity
          const yPos = (scrollY - offsetTop) * speed;
          el.style.transform = `translateY(${yPos}px)`;
        }
      });
    }

    document.addEventListener('DOMContentLoaded', function () {
      window.addEventListener('scroll', throttle(handleParallaxScroll, 16)); // 60fps-ish
    });
  </script>
  
<script>
document.addEventListener("DOMContentLoaded", function () {
  const lazySections = document.querySelectorAll('.lazy-background');

  const observer = new IntersectionObserver(entries => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const el = entry.target;
        const bgUrl = el.dataset.bg;

        // Create a new image to preload the background
        const img = new Image();
        img.src = bgUrl;

        img.onload = function () {
          // Only set background when fully loaded
          el.style.backgroundImage = `url('${bgUrl}')`;
          el.classList.add('loaded');
        };

        // Stop observing this element
        observer.unobserve(el);
      }
    });
  }, {
    rootMargin: '200px', // Preload a bit before the element enters the viewport
    threshold: 0.1
  });

  lazySections.forEach(section => observer.observe(section));
});
</script>

 


</body>

</html>