:root {
    --primary-color: #F27329;
    --secondary-color: #141313;
    --accent-color: #F29E6D;
    --light-color: #F2F2F2
}

.hero {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 2rem;
    background-color: var(--secondary-color);
    color: var(--light-color);
    min-height: 90vh;
    position: relative;
    overflow: hidden;
}

.hero-content,
.scrolling-images {
    flex: 1;
    height: 100%
}

.hero-content {
    flex: 1;
    padding: 2rem 0;
    /* text-align: center; */
    z-index: 2;
}

.hero h1 {
    font-size: clamp(1rem, 4vw, 2.2rem);
    margin-bottom: 1.5rem;
    color: #fff;
    line-height: 1.2;
}

.hero-subtitle {
    font-size: clamp(1rem, 1vw, 1rem);
    color: #fff;
    margin-bottom: 2rem;
    max-width: 800px;
    margin-left: auto;
    margin-right: auto;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 1rem;
    /* justify-content: center; */
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.primary-btn {
    background-color: #F27329;
    color: white;
    padding: 1rem 2rem;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: background-color 0.3s;
}

.primary-btn:hover {
    background-color: #e05a1a;
}

.secondary-btn {
    background-color: transparent;
    color: #F27329;
    padding: 1rem 2rem;
    border: 2px solid #F27329;
    border-radius: 5px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s;
}

.secondary-btn:hover {
    background-color: #F27329;
    color: white;
}

.hero-features {
    display: flex;
    /* justify-content: center; */
    gap: 2rem;
    margin-top: 2rem;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
}

.feature-number {
    font-size: clamp(1.5rem, 3vw, 2.5rem);
    font-weight: bold;
    color: #F27329;
}

.feature-text {
    font-size: clamp(0.8rem, 1.5vw, 1rem);
    color: #fff;
    text-align: center;
}

.hero-icons {
    display: flex;
    gap: 2rem;
    margin-top: 2rem
}

.hero-icons div {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: .5rem
}

.hero-icons svg {
    width: 50px;
    height: 50px;
    fill: var(--light-color)
}

.scrolling-images {
    overflow: hidden;
    position: relative
}

.image-track {
    display: flex;
    width: 400%;
    height: 100%;
    animation: scrollImages 12s linear infinite
}

.image-track img {
    width: 25%;
    height: 100%;
    object-fit: cover;
    flex-shrink: 0
}

@keyframes scrollImages {
    0% {
        transform: translateX(0)
    }
    100% {
        transform: translateX(-50%)
    }
}

@media (max-width: 1024px) {
    .hero {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .hero-content {
        padding: 1rem;
    }

    .scrolling-images {
        display: none;
    }
}

@media (max-width: 768px) {
    .hero {
        min-height: 80vh;
    }

    .cta-buttons {
        flex-direction: column;
        width: 100%;
        max-width: 300px;
        margin-left: auto;
        margin-right: auto;
    }

    .primary-btn, .secondary-btn {
        width: 100%;
        text-align: center;
    }

    .hero-features {
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 1rem 0.5rem;
    }

    .hero-content {
        padding: 0.5rem;
    }

    .feature {
        min-width: 100px;
    }
}




#contact,
.about-heading,
.card,
.contact-section,
.footer-bottom,
footer {
    text-align: center
}

#about,
#contact {
    background-color: #f9f9f9
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Public Sans', sans-serif;
    overflow-x: hidden;
    color: #757575;
    line-height: 1.5;
}

.footer-links ul li a,
.logo,
.nav-link,
.topan {
    text-decoration: none
}

html {
    scroll-behavior: smooth
}

.header-wrapper.scrolled .navbar,
.logo-container {
    left: 0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, .1);
    top: 0
}

.header-wrapper {
    position: relative;
    width: 100%;
    z-index: 1000
}

.logo-container,
.navbar {
    background: #141313
}

.contacta-info i,
.nav-link {
    color: #fff;
    font-size: 18px
}

.logo img,
.logo-container {
    transition: .3s ease-in-out
}

.mobile-menu-btn:focus {
    outline: 0
}

.business-hours {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px
}

.contacta-info {
    display: flex;
    gap: 20px;
    font-size: 18px
}

.contacta-info .icon {
    vertical-align: middle;
    margin-right: 10px;
    color: #333
}

.contacta-info span {
    display: flex;
    align-items: center;
    gap: 6px
}

.logo-container {
    position: absolute;
    z-index: 1002;
    padding: 30px 2rem;
    height: 79px;
    display: flex;
    align-items: center
}

.nav-item,
.navbar {
    align-items: center
}

.logo {
    display: block
}

.nav-item,
.nav-link,
.nav-menu,
.navbar {
    display: flex
}

.logo img {
    height: 66px;
    width: auto
}

.navbar {
    padding: 0 2rem 0 300px;
    height: 80px;
    transition: background-color .4s ease-in-out;
    position: relative;
    z-index: 1000
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%
}

.nav-menu {
    list-style: none;
    gap: 45px;
    margin: 0
}

.nav-link {
    font-weight: 500;
    align-items: center;
    gap: 6px;
    transition: color .2s
}

.section-title,
h2 {
    font-weight: 700
}

.nav-link:hover {
    color: #f2a444
}

.nav-link::after {
    content: '';
    font-size: 20px;
    font-weight: 400;
    margin-left: 2px
}

.header-wrapper.scrolled .navbar {
    position: fixed;
    width: 100%;
    background-color: #fff
}

.header-wrapper.scrolled .logo-container {
    position: fixed;
    top: 0;
    height: 80px;
    background-color: #fff
}

.header-wrapper.scrolled .logo img {
    height: 50px
}

.header-wrapper.scrolled .nav-link {
    color: #272e3d
}

.cta-btn {
    background-color: #f27329;
    padding: 10px 20px;
    border-radius: 5px;
    transition: background-color .3s
}

.cta-btn:hover {
    background-color: #bf152d
}

.mobile-menu-btn {
    display: none;
    background: 0 0;
    border: none;
    cursor: pointer;
    padding: 10px;
    position: fixed;
    right: 20px;
    top: 25px;
    z-index: 1003
}

.hamburger,
.hamburger:after,
.hamburger:before {
    display: block;
    width: 24px;
    height: 2px;
    background: #f27329
}

.hamburger {
    position: relative;
    transition: background .2s ease-out
}

.hamburger:after,
.hamburger:before {
    content: '';
    position: absolute;
    transition: .2s ease-out
}

.hamburger:before {
    top: -8px
}

.hamburger:after {
    bottom: -8px
}

.mobile-menu-btn.active .hamburger {
    background: 0 0
}

.mobile-menu-btn.active .hamburger:before {
    transform: rotate(45deg);
    top: 0
}

.mobile-menu-btn.active .hamburger:after {
    transform: rotate(-45deg);
    bottom: 0
}

.mobile-contacta {
    display: none
}

@media (min-width:992px) and (max-width:1024px) {
    .business-hours,
    .contacta-info {
        font-size: 16px
    }
}

@media (max-width:991px) {
    .header-wrapper {
        position: fixed;
        top: 0;
        width: 100%
    }
    .top-bar {
        display: none
    }
    .mobile-menu-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px
    }
    .logo-container {
        position: fixed;
        width: 100%;
        height: 80px;
        padding: 10px 2rem
    }
    .logo img {
        height: 50px
    }
    .navbar {
        position: fixed;
        top: 0;
        right: -300px;
        width: 300px;
        height: auto;
        background: #fff;
        padding: 100px 20px 20px;
        transform: translateX(100%);
        transition: transform .1s ease-in-out;
        box-shadow: -2px 0 10px rgba(0, 0, 0, .1);
        overflow-y: auto;
        margin-top: 0;
        z-index: 1000
    }
    .nav-item,
    .nav-link,
    .nav-menu {
        width: 100%
    }
    .navbar.active {
        transform: translateX(-300px)
    }
    body.menu-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
        height: 100%
    }
    .nav-container {
        flex-direction: column;
        align-items: flex-start
    }
    .nav-menu {
        flex-direction: column;
        gap: 0
    }
    .nav-link {
        color: #272e3d;
        padding: 15px 0;
        display: flex;
        justify-content: space-between;
        border-bottom: 1px solid rgba(0, 0, 0, .1)
    }
    .mobile-contacta {
        display: block;
        margin-top: 30px;
        padding-top: 30px;
        border-top: 1px solid rgba(0, 0, 0, .1)
    }
    .mobile-contacta .contacta-info {
        flex-direction: column;
        gap: 15px
    }
    .mobile-contacta .contacta-info p {
        color: #666;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0
    }
    .mobile-contacta .contacta-info i {
        color: #f27329;
        width: 16px;
        text-align: center
    }
    .cta-btn {
        margin-top: 30px;
        text-align: center
    }
}

footer {
    overflow: hidden
}

.contact-container,
.contact-item,
.footer-container,
.icon {
    display: flex;
    
}

@media (max-width:767px) {
    .logo-container {
        padding: 10px 20px
    }
    .mobile-menu-btn {
        right: 15px
    }
    .navbar {
        width: 100%;
        right: -100%
    }
    .navbar.active {
        transform: translateX(-100%)
    }
}

.why-us-bar {
    display: flex;
    overflow: hidden
}

.about-us-heading,
.about-us-right,
.section-heading,
.service-item,
.why-us-heading,
.why-us-item {
    text-align: center
}

.about-us {
    background-color: #fff
}

.about-us {
    color: #0e1f32;
    padding: 40px 20px
}

.container2 {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.about-us-left {
    width: 50%;
    padding: 20px;
}

.about-us-heading {
    font-size: 2.5rem;
    font-weight: 700;
    color: #0e1f32
}

.about-us-description {
    font-size: 1rem;
    color: #0e1f32;
    margin-top: 20px
}

.why-us-heading {
    font-size: 1.5rem;
    font-weight: 700;
    margin-top: 30px;
    color: #0e1f32
}

.why-us-bar {
    justify-content: space-between;
    margin-top: 20px;
    border: 1px solid #0e1f32;
    border-radius: 10px
}

.why-us-item {
    width: 33%;
    padding: 20px;
    border-right: 1px solid #0e1f32
}

.why-us-item:last-child {
    border-right: none
}

.why-us-item h4 {
    font-size: 1.2rem;
    color: var(--red)
}

.why-us-item p {
    font-size: 1rem;
    color: #0e1f32
}

.about-us-right {
    width: 50%
}

.about-us-image {
    width: 100%;
    height: auto;
    border-radius: 10px
}

@media (max-width:768px) {
    .container2 {
        flex-direction: column;
        align-items: center
    }
    .about-us-left,
    .about-us-right {
        width: 100%
    }
    .why-us-bar {
        flex-direction: column
    }
    .why-us-item {
        width: 100%;
        border-right: none;
        border-bottom: 1px solid var(--white)
    }
    .why-us-item:last-child {
        border-bottom: none
    }
}

.services {
    padding: 4rem 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.services-heading {
    font-size: clamp(1.5rem, 3vw, 2rem);
    color: #F27329;
    text-align: center;
    margin-bottom: 1rem;
}

.services-subheading {
    font-size: clamp(1rem, 1.5vw, 1.2rem);
    text-align: center;
    margin-bottom: 3rem;
    color: #666;
}

.service-category {
    margin-bottom: 4rem;
}

.service-category h3 {
    font-size: clamp(1.2rem, 2vw, 1.5rem);
    color: #333;
    margin-bottom: 1rem;
    /* text-align: center; */
}

.category-description {
    font-size: clamp(0.9rem, 1.2vw, 1rem);
    text-align: center;
    margin-bottom: 2rem;
    color: #666;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
    padding: 1rem;
}

/* UPDATED SERVICE CARD - KEY CHANGES FOR BUTTON ALIGNMENT */
.service-card {
    position: relative;
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    border-top: 4px solid #F27329;
    
    /* KEY ADDITIONS FOR BUTTON ALIGNMENT */
    display: flex;
    flex-direction: column;
    min-height: 500px; /* Ensures all cards have same minimum height */
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #F27329;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.service-card img {
    width: 100%;
    height: 280px;
    object-fit: cover;
    padding: 1rem;
}

.service-card h3 {
    font-size: clamp(1.1rem, 1.5vw, 1.3rem);
    color: #333;
    margin: 1rem;
}

.service-features {
    list-style: none;
    padding: 0 1rem;
    margin-bottom: 1.5rem;
    
    /* KEY ADDITION: Allow features section to grow and push buttons down */
    flex-grow: 1;
}

.service-features li {
    font-size: clamp(0.9rem, 1.2vw, 1rem);
    color: #666;
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
    position: relative;
}

.service-features li:before {
    content: "✓";
    color: #F27329;
    position: absolute;
    left: 0;
}

.service-cta-container {
    display: flex;
    gap: 1rem;
    padding: 1rem;
    flex-wrap: wrap;
    
    /* KEY ADDITION: Push buttons to bottom of card */
    margin-top: auto;
}

.service-cta {
    flex: 1;
    min-width: 120px;
    padding: 0.8rem;
    border-radius: 5px;
    text-align: center;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.service-cta.call-cta {
    background-color: #F27329;
    color: white;
}

.service-cta.email-cta {
    background-color: white;
    color: #F27329;
    border: 2px solid #F27329;
}

.service-cta:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.service-cta.call-cta:hover {
    background-color: #e05a1a;
}

.service-cta.email-cta:hover {
    background-color: #F27329;
    color: white;
}


a {
    text-decoration: none !important;
}

/* Testimonials Section Styles */
.testimonials-section {
    background-color: #f7f7f7;
    padding: 60px 0;
    position: relative;
}

.testimonials-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.testimonials-header {
    text-align: center;
    margin-bottom: 40px;
}

.testimonials-header h2 {
    color: #F27329;
    font-size: 2rem;
    margin-bottom: 15px;
}

.testimonials-subheading {
    color: #666;
    font-size: 1.1rem;
    max-width: 600px;
    margin: 0 auto;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.testimonial-card {
    background: white;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.12);
}

.testimonial-card::before {
    content: '"';
    position: absolute;
    top: 20px;
    right: 30px;
    font-size: 80px;
    color: #F27329;
    opacity: 0.1;
    font-family: serif;
    line-height: 1;
}

.testimonial-rating {
    margin-bottom: 20px;
}

.star {
    color: #FFD700;
    font-size: 1.2rem;
    margin-right: 2px;
}

.testimonial-text {
    font-size: 1rem;
    line-height: 1.6;
    color: #333;
    margin-bottom: 25px;
    font-style: italic;
}

.testimonial-author {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.author-info {
    display: flex;
    flex-direction: column;
}

.author-name {
    font-weight: 600;
    color: #F27329;
    font-size: 1.1rem;
}

.author-title {
    color: #666;
    font-size: 0.9rem;
}

.testimonial-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 10px;
    padding-top: 10px;
    border-top: 1px solid #eee;
}

.service-type {
    background-color: #F27329;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
}

.testimonial-date {
    color: #888;
    font-size: 0.8rem;
}

/* UPDATED MEDIA QUERIES FOR SERVICE CARDS */
@media (max-width: 768px) {
    .services {
        padding: 2rem 1rem;
    }

    .service-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        min-height: 450px; /* Adjust minimum height for mobile */
    }

    .service-card img {
        height: 180px;
    }

    .service-cta-container {
        flex-direction: column;
    }

    .service-cta {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .services {
        padding: 1.5rem 0.5rem;
    }

    .service-card {
        margin: 0 0.5rem;
        min-height: 400px; /* Adjust minimum height for small mobile */
    }

    .service-features li {
        font-size: 0.9rem;
    }
}

.testimonials-subheading {
    color: #666;
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.testimonial {
    min-width: 100%;
    padding: 2rem;
    background-color: white;
    border-radius: 12px;
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    margin: 1rem;
}

.testimonial-rating {
    color: #FFD700;
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.testimonial-text {
    font-style: italic;
    font-size: 1.2rem;
    color: #555;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.testimonial-author {
    margin-bottom: 1rem;
}

.author-name {
    font-weight: bold;
    font-size: 1.1rem;
    color: #F27329;
    margin-right: 0.5rem;
}

.author-title {
    color: #666;
    font-size: 0.9rem;
}

.testimonial-meta {
    display: flex;
    justify-content: space-between;
    color: #888;
    font-size: 0.9rem;
    border-top: 1px solid #eee;
    padding-top: 1rem;
}

.service-type {
    background-color: #F27329;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
}

@media (max-width:768px) {
    .service-grid {
        grid-template-columns: 1fr
    }
    .service-card {
        padding: 1.5rem
    }
    .services-heading {
        font-size: 2rem
    }
    .service-category h2 {
        font-size: 1.5rem
    }
    .service-card h3 {
        font-size: 1.3rem
    }
    .service-card p {
        font-size: .9rem
    }
}

footer {
    padding: 20px 10px
}

.contact-section {
    padding: 40px 20px;
    background-color: #191659;
    color: #fff
}

.section-title {
    font-size: 32px;
    color: #bf8d30;
    margin-bottom: 20px
}

.contact-container {
    display: flex;
     /* flex-wrap: wrap; */
    gap: 20px; 
    flex-wrap: wrap;
    background-color: #fcf9f9
}

.footer,
footer {
    background-color: #191659
}

.contact-item {
    align-items: center;
    margin-bottom: 15px
}

.icon {
    width: 24px;
    height: 24px;
    color: #bf8d30;
    margin-right: 15px
}

.contact-item p {
    font-size: 16px;
    color: #191659;
    margin: 0
}

.map-container {
    flex: 1;
    padding: 20px
}

.map {
    width: 100%;
    height: 250px;
    border: 0;
    border-radius: 10px
}

.footer {
    color: #fff;
    padding: 40px 20px
}

.footer-container {
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
    position: relative
}

.logo-small {
    width: 50px;
    height: auto;
    margin-right: 10px
}

.footer-logo {
    position: absolute;
    left: 50%;
    transform: translateX(-50%)
}

.logo-center {
    width: 80px;
    height: auto
}

.footer-links {
    flex: 1;
    text-align: right
}

.footer-links h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #bf8d30
}

.footer-links ul {
    list-style: none;
    padding: 0
}

.footer-links ul li {
    margin-bottom: 8px
}

.footer-links ul li a {
    color: #fff;
    transition: color .3s
}

.footer-links ul li a:hover {
    color: #bf8d30
}

.footer-bottom {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 3px solid #bf8d30;
    font-size: 17px
}

@media (max-width:768px) {
    .menu-toggle {
        display: block
    }
    .nav-links {
        display: none;
        position: absolute;
        top: 60px;
        right: 0;
        background-color: #f27329;
        flex-direction: column;
        padding: 20px
    }
    .nav-links.active {
        display: flex
    }
    .hero h1 {
        font-size: 2.5rem
    }
    .hero p {
        font-size: 1rem
    }
    .hero-icons {
        display: none
    }
    .contact-container,
    .footer-container {
        flex-direction: column;
        text-align: center
    }
    .contact-item {
        justify-content: center
    }
    .footer-logo {
        position: relative;
        left: auto;
        transform: none;
        margin-top: 20px
    }
    .footer-links {
        text-align: center;
        margin-top: 20px
    }
}

@keyframes moveUpDown {
    0%,
    100% {
        transform: translateY(0)
    }
    50% {
        transform: translateY(-10px)
    }
}

.footer-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center
}

.footer-container a,
.footer-container p {
    color: #fff;
    text-decoration: none;
    margin: 5px 0
}

.footer-container img {
    width: 50px;
    height: 50px;
    margin-top: 10px
}

#contact,
iframe {
    width: 100%
}

@media (max-width:600px) {
    .footer-container p {
        font-size: 14px
    }
}

.section-heading {
    font-size: 28px;
    font-weight: 700;
    color: #f27329;
    margin-bottom: 20px
}

.contact-container {
    max-width: 1200px;
    margin: 0 auto
}

.contact-container {
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px
}

.contact-map {
    flex: 1;
}


.contact-info,
.contact-map {
    width: 50%
}

.contact-map iframe {
    width: 100%;
    height: 350px;
    border: 0
}

.contact-info {
    text-align: left;
    padding: 10px
}

.contact-info address {
    font-size: 16px;
    line-height: 1.6;
    font-style: normal
}

.contact-info a {
    color: #000;
    text-decoration: underline;
}

.social-icons {
    display: flex;
    gap: 15px;
    margin-top: 20px;
}

.social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #F27329;
    color: white;
    transition: all 0.3s ease;
    text-decoration: none;
}

.social-icon:hover {
    background-color: #e05a1a;
    transform: translateY(-3px);
}

.social-icon i {
    width: 20px;
    height: 20px;
}

h2 {
    font-size: 2rem;
    color: #f27329;
    margin-bottom: 40px
}

@media (max-width:768px) {
    .contact-container {
        flex-direction: column;
        text-align: center
    }
    .contact-info,
    .contact-map {
        width: 100%
    }
    /* .contact-info {
        text-align: center
    } */
    section {
        flex-direction: column;
        align-items: center
    }
}

a {
    text-decoration: none;
}

/* Gallery Styles */
.gallery-section {
    padding: 60px 0;
    background-color: #f7f7f7;
}

.gallery-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.gallery-heading {
    text-align: center;
    color: #F27329;
    font-size: 2rem;
    margin-bottom: 10px;
}

.gallery-subheading {
    text-align: center;
    color: #666;
    margin-bottom: 40px;
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px 0;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: translateY(-5px);
}

.gallery-image {
    width: 100%;
    height: 250px;
    object-fit: cover;
    display: block;
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-image {
    transform: scale(1.05);
}

.gallery-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
    padding: 20px;
    color: white;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    transform: translateY(0);
}

.gallery-caption {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Lightbox Styles */
.lightbox {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.9);
    padding: 20px;
    box-sizing: border-box;
}

.lightbox-content {
    max-width: 90%;
    max-height: 90vh;
    margin: auto;
    display: block;
    border-radius: 4px;
}

.lightbox-close {
    position: absolute;
    top: 15px;
    right: 25px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.lightbox-close:hover {
    color: #F27329;
}

.lightbox-caption {
    color: white;
    text-align: center;
    padding: 10px 0;
    font-size: 1.2rem;
}

@media (max-width: 768px) {
    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .gallery-image {
        height: 200px;
    }

    .gallery-heading {
        font-size: 1.5rem;
    }
}


@media (max-width: 768px) {
    .testimonials-section {
        padding: 40px 0;
    }

    .testimonials-header h2 {
        font-size: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .testimonial-card {
        padding: 20px;
    }
}
